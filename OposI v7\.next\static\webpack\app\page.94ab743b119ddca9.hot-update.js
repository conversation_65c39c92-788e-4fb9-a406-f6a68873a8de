"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx":
/*!*************************************************************!*\
  !*** ./src/components/flashcards/FlashcardStudyOptions.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiBookOpen,FiClock,FiHelpCircle,FiLoader,FiRefreshCw,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst FlashcardStudyOptions = (param)=>{\n    let { isOpen, onClose, onSelectStudyType, estadisticas, isLoading = false } = param;\n    const opcionesEstudio = [\n        {\n            tipo: 'dificiles',\n            label: 'Más difíciles',\n            descripcion: 'Tarjetas que has marcado como difíciles más frecuentemente',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiHelpCircle, {\n                className: \"text-red-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 25,\n                columnNumber: 13\n            }, undefined),\n            color: 'red-600',\n            bgColor: 'bg-red-100',\n            hoverBgColor: 'hover:bg-red-200'\n        },\n        {\n            tipo: 'aleatorias',\n            label: 'Aleatorias',\n            descripcion: 'Selección aleatoria de tarjetas de la colección',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                className: \"text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, undefined),\n            color: 'purple-600',\n            bgColor: 'bg-purple-100',\n            hoverBgColor: 'hover:bg-purple-200'\n        },\n        {\n            tipo: 'no-recientes',\n            label: 'No estudiadas recientemente',\n            descripcion: 'Tarjetas que no has revisado en mucho tiempo',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                className: \"text-orange-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, undefined),\n            color: 'orange-600',\n            bgColor: 'bg-orange-100',\n            hoverBgColor: 'hover:bg-orange-200'\n        },\n        {\n            tipo: 'nuevas',\n            label: 'Nuevas',\n            descripcion: 'Tarjetas que nunca has estudiado',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiBookOpen, {\n                className: \"text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, undefined),\n            color: 'green-600',\n            bgColor: 'bg-green-100',\n            hoverBgColor: 'hover:bg-green-200'\n        },\n        {\n            tipo: 'aprendiendo',\n            label: 'En aprendizaje',\n            descripcion: 'Tarjetas que estás aprendiendo actualmente',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiLoader, {\n                className: \"text-yellow-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, undefined),\n            color: 'yellow-600',\n            bgColor: 'bg-yellow-100',\n            hoverBgColor: 'hover:bg-yellow-200'\n        }\n    ];\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"Opciones de Estudio\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-3\",\n                            children: \"Elige el tipo de estudio que prefieras. Cada opci\\xf3n te permitir\\xe1 enfocar tu aprendizaje de manera diferente:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800 font-medium\",\n                                children: [\n                                    \"ℹ️ Importante: Estos estudios adicionales son complementarios y \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"no afectan al algoritmo de repetici\\xf3n espaciada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 79\n                                    }, undefined),\n                                    '. Para el estudio oficial que cuenta para tu progreso, usa el bot\\xf3n \"Estudiar\" principal.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: opcionesEstudio.map((opcion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onSelectStudyType(opcion.tipo),\n                            className: \"p-4 border rounded-lg text-left transition-all duration-200 \".concat(opcion.hoverBgColor, \" \").concat(opcion.bgColor, \" border-gray-200 hover:border-gray-300\"),\n                            disabled: isLoading,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-1\",\n                                        children: opcion.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-\".concat(opcion.color, \" mb-1\"),\n                                                children: opcion.tipo === 'programadas' ? \"\".concat(opcion.label, \" (\").concat(estadisticas ? estadisticas.paraHoy : 0, \")\") : opcion.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: opcion.descripcion\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined)\n                        }, opcion.tipo, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex justify-end space-x-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                        children: \"Cancelar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardStudyOptions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardStudyOptions);\nvar _c;\n$RefreshReg$(_c, \"FlashcardStudyOptions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx\n"));

/***/ })

});