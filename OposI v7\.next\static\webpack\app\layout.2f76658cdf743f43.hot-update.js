"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/hooks/useAutoLogout.ts":
/*!************************************!*\
  !*** ./src/hooks/useAutoLogout.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\nconst useAutoLogout = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { timeoutMinutes = 5, warningMinutes = 0.5, enabled = true } = options;\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const warningTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const warningShownRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const pageVisibleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    // Convertir minutos a milisegundos\n    const timeoutMs = timeoutMinutes * 60 * 1000;\n    const warningMs = warningMinutes * 60 * 1000;\n    const clearTimeouts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[clearTimeouts]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            if (warningTimeoutRef.current) {\n                clearTimeout(warningTimeoutRef.current);\n                warningTimeoutRef.current = null;\n            }\n        }\n    }[\"useAutoLogout.useCallback[clearTimeouts]\"], []);\n    const handleLogout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleLogout]\": async ()=>{\n            try {\n                // Limpiar timeouts antes del logout\n                clearTimeouts();\n                await logout();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Sesión cerrada por inactividad', {\n                    duration: 4000,\n                    position: 'top-center',\n                    id: 'auto-logout' // Evitar duplicados\n                });\n            } catch (error) {\n                console.error('Error al cerrar sesión por inactividad:', error);\n                // Intentar logout forzado si hay error\n                try {\n                    window.location.href = '/login';\n                } catch (redirectError) {\n                    console.error('Error al redirigir:', redirectError);\n                }\n            }\n        }\n    }[\"useAutoLogout.useCallback[handleLogout]\"], [\n        logout,\n        clearTimeouts\n    ]);\n    const showWarning = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[showWarning]\": ()=>{\n            if (!warningShownRef.current) {\n                warningShownRef.current = true;\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.warning('⚠️ Tu sesión se cerrará en 30 segundos por inactividad', {\n                    duration: 4000,\n                    position: 'top-center',\n                    id: 'auto-logout-warning',\n                    style: {\n                        background: '#f59e0b',\n                        color: '#fff',\n                        fontWeight: 'bold'\n                    }\n                });\n            }\n        }\n    }[\"useAutoLogout.useCallback[showWarning]\"], []);\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n            if (!enabled || !user) return;\n            lastActivityRef.current = Date.now();\n            warningShownRef.current = false;\n            clearTimeouts();\n            // Configurar timeout para mostrar advertencia\n            if (warningMs > 0) {\n                warningTimeoutRef.current = setTimeout({\n                    \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                        showWarning();\n                    }\n                }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs - warningMs);\n            }\n            // Configurar timeout para logout\n            timeoutRef.current = setTimeout({\n                \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                    handleLogout();\n                }\n            }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs);\n        }\n    }[\"useAutoLogout.useCallback[resetTimer]\"], [\n        enabled,\n        user,\n        timeoutMs,\n        warningMs,\n        clearTimeouts,\n        handleLogout,\n        showWarning\n    ]);\n    // Eventos que indican actividad del usuario\n    const activityEvents = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click'\n    ];\n    const handleActivity = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleActivity]\": (event)=>{\n            // Evitar resetear el timer por movimientos mínimos del mouse\n            if (event.type === 'mousemove') {\n                const now = Date.now();\n                if (now - lastActivityRef.current < 1000) {\n                    return;\n                }\n            }\n            resetTimer();\n        }\n    }[\"useAutoLogout.useCallback[handleActivity]\"], [\n        resetTimer\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            if (!enabled || !user) {\n                clearTimeouts();\n                return;\n            }\n            // Inicializar timer\n            resetTimer();\n            // Agregar event listeners\n            activityEvents.forEach({\n                \"useAutoLogout.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useAutoLogout.useEffect\"]);\n            // Cleanup\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                    activityEvents.forEach({\n                        \"useAutoLogout.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useAutoLogout.useEffect\"]);\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        enabled,\n        user,\n        resetTimer,\n        handleActivity,\n        clearTimeouts\n    ]);\n    // Cleanup al desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        clearTimeouts\n    ]);\n    return {\n        resetTimer,\n        clearTimeouts,\n        isEnabled: enabled && !!user\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoLogout);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoLogout.ts\n"));

/***/ })

});