"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/testsService.ts":
/*!******************************************!*\
  !*** ./src/lib/supabase/testsService.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crearPreguntaTest: () => (/* binding */ crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* binding */ crearTest),\n/* harmony export */   guardarPreguntasTest: () => (/* binding */ guardarPreguntasTest),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* binding */ obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* binding */ obtenerEstadisticasTest),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* binding */ obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* binding */ obtenerPreguntasTestCount),\n/* harmony export */   obtenerTestPorId: () => (/* binding */ obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* binding */ obtenerTests),\n/* harmony export */   registrarRespuestaTest: () => (/* binding */ registrarRespuestaTest)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea un nuevo test\n */ async function crearTest(titulo, descripcion, documentosIds) {\n    try {\n        var _data_, _data_1;\n        console.log('📝 Creando nuevo test:', titulo);\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('❌ No hay usuario autenticado para crear test');\n            return null;\n        }\n        console.log('👤 Usuario autenticado:', user.id);\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').insert([\n            {\n                titulo,\n                descripcion,\n                documentos_ids: documentosIds,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('❌ Error al crear test:', error);\n            return null;\n        }\n        console.log('✅ Test creado exitosamente:', data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id);\n        return (data === null || data === void 0 ? void 0 : (_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : _data_1.id) || null;\n    } catch (error) {\n        console.error('💥 Error inesperado al crear test:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los tests del usuario actual\n */ async function obtenerTests() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').select('*').eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener tests:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener tests:', error);\n        return [];\n    }\n}\n/**\n * Obtiene un test por su ID (solo si pertenece al usuario actual)\n */ async function obtenerTestPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('tests').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener test:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener test:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva pregunta para un test\n */ async function crearPreguntaTest(testId, pregunta, opcionA, opcionB, opcionC, opcionD, respuestaCorrecta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').insert([\n        {\n            test_id: testId,\n            pregunta,\n            opcion_a: opcionA,\n            opcion_b: opcionB,\n            opcion_c: opcionC,\n            opcion_d: opcionD,\n            respuesta_correcta: respuestaCorrecta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear pregunta de test:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las preguntas de un test\n */ async function obtenerPreguntasPorTestId(testId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*').eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener preguntas de test:', error);\n        return [];\n    }\n    return data || [];\n}\n/**\n * Obtiene el número de preguntas de un test\n */ async function obtenerPreguntasTestCount(testId) {\n    const { count, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener conteo de preguntas:', error);\n        return 0;\n    }\n    return count || 0;\n}\n/**\n * Guarda múltiples preguntas de test\n */ async function guardarPreguntasTest(preguntas) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').insert(preguntas);\n    if (error) {\n        console.error('Error al guardar preguntas de test:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Registra la respuesta de un usuario a una pregunta de test\n */ async function registrarRespuestaTest(testId, preguntaId, respuestaUsuario, esCorrecta) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').insert([\n        {\n            test_id: testId,\n            pregunta_id: preguntaId,\n            respuesta_seleccionada: respuestaUsuario,\n            es_correcta: esCorrecta,\n            fecha_respuesta: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error al registrar respuesta de test:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Obtiene estadísticas generales de todos los tests\n */ async function obtenerEstadisticasGeneralesTests() {\n    // Obtener todas las respuestas\n    const { data: respuestas, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').select('*');\n    if (error) {\n        console.error('Error al obtener estadísticas de tests:', error);\n        return {\n            totalTests: 0,\n            totalPreguntas: 0,\n            totalRespuestasCorrectas: 0,\n            totalRespuestasIncorrectas: 0,\n            porcentajeAcierto: 0\n        };\n    }\n    // Obtener tests únicos respondidos\n    const testsUnicos = new Set((respuestas === null || respuestas === void 0 ? void 0 : respuestas.map((r)=>r.test_id)) || []);\n    // Obtener preguntas únicas respondidas\n    const preguntasUnicas = new Set((respuestas === null || respuestas === void 0 ? void 0 : respuestas.map((r)=>r.pregunta_id)) || []);\n    // Contar respuestas correctas e incorrectas\n    const correctas = (respuestas === null || respuestas === void 0 ? void 0 : respuestas.filter((r)=>r.es_correcta).length) || 0;\n    const incorrectas = ((respuestas === null || respuestas === void 0 ? void 0 : respuestas.length) || 0) - correctas;\n    // Calcular porcentaje de acierto\n    const porcentaje = respuestas && respuestas.length > 0 ? Math.round(correctas / respuestas.length * 100) : 0;\n    return {\n        totalTests: testsUnicos.size,\n        totalPreguntas: preguntasUnicas.size,\n        totalRespuestasCorrectas: correctas,\n        totalRespuestasIncorrectas: incorrectas,\n        porcentajeAcierto: porcentaje\n    };\n}\n/**\n * Obtiene estadísticas detalladas de un test específico\n */ async function obtenerEstadisticasTest(testId) {\n    // Obtener todas las respuestas del test\n    const { data: respuestas, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('estadisticas_test').select('*').eq('test_id', testId);\n    if (error) {\n        console.error('Error al obtener estadísticas del test:', error);\n        return {\n            testId: testId,\n            totalPreguntas: 0,\n            totalCorrectas: 0,\n            totalIncorrectas: 0,\n            porcentajeAcierto: 0,\n            fechasRealizacion: [],\n            preguntasMasFalladas: []\n        };\n    }\n    // Obtener preguntas del test\n    const { data: preguntas } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('preguntas_test').select('*').eq('test_id', testId);\n    // Contar respuestas correctas e incorrectas\n    const correctas = (respuestas === null || respuestas === void 0 ? void 0 : respuestas.filter((r)=>r.es_correcta).length) || 0;\n    const incorrectas = ((respuestas === null || respuestas === void 0 ? void 0 : respuestas.length) || 0) - correctas;\n    // Calcular porcentaje de acierto\n    const porcentaje = respuestas && respuestas.length > 0 ? Math.round(correctas / respuestas.length * 100) : 0;\n    // Obtener fechas únicas de realización\n    const fechasSet = new Set();\n    respuestas === null || respuestas === void 0 ? void 0 : respuestas.forEach((r)=>{\n        const fecha = new Date(r.fecha_respuesta);\n        fechasSet.add(\"\".concat(fecha.getDate(), \"/\").concat(fecha.getMonth() + 1, \"/\").concat(fecha.getFullYear()));\n    });\n    const fechasUnicas = Array.from(fechasSet);\n    // Calcular preguntas más falladas\n    const fallosPorPregunta = new Map();\n    respuestas === null || respuestas === void 0 ? void 0 : respuestas.forEach((respuesta)=>{\n        const actual = fallosPorPregunta.get(respuesta.pregunta_id) || {\n            fallos: 0,\n            aciertos: 0\n        };\n        if (respuesta.es_correcta) {\n            actual.aciertos++;\n        } else {\n            actual.fallos++;\n        }\n        fallosPorPregunta.set(respuesta.pregunta_id, actual);\n    });\n    // Convertir a array y ordenar por fallos\n    const preguntasFalladas = Array.from(fallosPorPregunta.entries()).map((param)=>{\n        let [id, stats] = param;\n        var _preguntas_find;\n        return {\n            preguntaId: id,\n            totalFallos: stats.fallos,\n            totalAciertos: stats.aciertos,\n            // Encontrar la pregunta correspondiente\n            pregunta: (preguntas === null || preguntas === void 0 ? void 0 : (_preguntas_find = preguntas.find((p)=>p.id === id)) === null || _preguntas_find === void 0 ? void 0 : _preguntas_find.pregunta) || 'Desconocida'\n        };\n    }).sort((a, b)=>b.totalFallos - a.totalFallos).slice(0, 5); // Tomar las 5 más falladas\n    return {\n        testId: testId,\n        totalPreguntas: (preguntas === null || preguntas === void 0 ? void 0 : preguntas.length) || 0,\n        totalCorrectas: correctas,\n        totalIncorrectas: incorrectas,\n        porcentajeAcierto: porcentaje,\n        fechasRealizacion: fechasUnicas,\n        preguntasMasFalladas: preguntasFalladas\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/testsService.ts\n"));

/***/ })

});