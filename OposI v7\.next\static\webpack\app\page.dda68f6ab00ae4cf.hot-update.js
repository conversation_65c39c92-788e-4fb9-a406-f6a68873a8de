"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards\n        .eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            return [];\n        }\n        // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]\n        // We need to transform this to a direct property numero_flashcards\n        return (data === null || data === void 0 ? void 0 : data.map((coleccion)=>({\n                ...coleccion,\n                // @ts-ignore Supabase types might not be perfect here for related counts\n                numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0 ? coleccion.flashcards[0].count : 0\n            }))) || [];\n    } catch (error) {\n        console.error('Error al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, fecha').in('flashcard_id', flashcardIds).order('fecha', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión mejorada para evitar errores 409\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        let progresoExiste = false;\n        // Intentar obtener progreso existente\n        const { data: progresoExistente, error: errorConsulta } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).single();\n        if (!errorConsulta && progresoExistente) {\n            factorFacilidad = progresoExistente.factor_facilidad || 2.5;\n            intervalo = progresoExistente.intervalo || 1;\n            repeticiones = progresoExistente.repeticiones || 0;\n            estado = progresoExistente.estado || 'nuevo';\n            progresoExiste = true;\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso usando insert o update según corresponda\n        let errorProgreso = null;\n        if (progresoExiste) {\n            // Actualizar progreso existente\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            }).eq('flashcard_id', flashcardId);\n            errorProgreso = error;\n        } else {\n            // Crear nuevo progreso\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').insert({\n                flashcard_id: flashcardId,\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            });\n            errorProgreso = error;\n        }\n        if (errorProgreso) {\n            console.error('Error al guardar progreso:', errorProgreso);\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ })

});