"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user, error: userError } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (userError) {\n            console.error('Error al obtener usuario:', userError);\n            return [];\n        }\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        console.log('Usuario autenticado:', user.id);\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            console.error('Detalles del error:', error);\n            return [];\n        }\n        console.log('Colecciones obtenidas:', (data === null || data === void 0 ? void 0 : data.length) || 0);\n        if (!data || data.length === 0) {\n            return [];\n        }\n        // Para cada colección, obtener el número de flashcards por separado\n        const coleccionesConConteo = await Promise.all(data.map(async (coleccion)=>{\n            try {\n                const { count, error: countError } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*', {\n                    count: 'exact',\n                    head: true\n                }).eq('coleccion_id', coleccion.id);\n                if (countError) {\n                    console.error('Error al contar flashcards para colección', coleccion.id, ':', countError);\n                    return {\n                        ...coleccion,\n                        numero_flashcards: 0\n                    };\n                }\n                return {\n                    ...coleccion,\n                    numero_flashcards: count || 0\n                };\n            } catch (error) {\n                console.error('Error al procesar colección', coleccion.id, ':', error);\n                return {\n                    ...coleccion,\n                    numero_flashcards: 0\n                };\n            }\n        }));\n        return coleccionesConConteo;\n    } catch (error) {\n        console.error('Error general al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, fecha').in('flashcard_id', flashcardIds).order('fecha', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión mejorada para evitar errores 409\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        let progresoExiste = false;\n        // Intentar obtener progreso existente\n        const { data: progresoExistente, error: errorConsulta } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).single();\n        if (!errorConsulta && progresoExistente) {\n            factorFacilidad = progresoExistente.factor_facilidad || 2.5;\n            intervalo = progresoExistente.intervalo || 1;\n            repeticiones = progresoExistente.repeticiones || 0;\n            estado = progresoExistente.estado || 'nuevo';\n            progresoExiste = true;\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso usando insert o update según corresponda\n        let errorProgreso = null;\n        if (progresoExiste) {\n            // Actualizar progreso existente\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            }).eq('flashcard_id', flashcardId);\n            errorProgreso = error;\n        } else {\n            // Crear nuevo progreso\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').insert({\n                flashcard_id: flashcardId,\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            });\n            errorProgreso = error;\n        }\n        if (errorProgreso) {\n            console.error('Error al guardar progreso:', errorProgreso);\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ })

});