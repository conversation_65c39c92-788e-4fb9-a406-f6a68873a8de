"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"685286f3b761\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjg1Mjg2ZjNiNzYxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/shared/components/AutoLogoutManager.tsx":
/*!**************************************************************!*\
  !*** ./src/features/shared/components/AutoLogoutManager.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAutoLogout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAutoLogout */ \"(app-pages-browser)/./src/hooks/useAutoLogout.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n/**\n * Componente que maneja el auto-logout por inactividad\n * Se ejecuta en toda la aplicación cuando el usuario está autenticado\n */ const AutoLogoutManager = ()=>{\n    _s();\n    // Configurar auto-logout con 5 minutos de inactividad\n    (0,_hooks_useAutoLogout__WEBPACK_IMPORTED_MODULE_1__.useAutoLogout)({\n        timeoutMinutes: 5,\n        warningMinutes: 0.5,\n        enabled: true // Habilitado por defecto\n    });\n    // Este componente no renderiza nada visible\n    return null;\n};\n_s(AutoLogoutManager, \"+Oi9LivNpFPokaFzIBJ1ikDyh3E=\", false, function() {\n    return [\n        _hooks_useAutoLogout__WEBPACK_IMPORTED_MODULE_1__.useAutoLogout\n    ];\n});\n_c = AutoLogoutManager;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AutoLogoutManager);\nvar _c;\n$RefreshReg$(_c, \"AutoLogoutManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9zaGFyZWQvY29tcG9uZW50cy9BdXRvTG9nb3V0TWFuYWdlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMEI7QUFDNEI7QUFFdEQ7OztDQUdDLEdBQ0QsTUFBTUUsb0JBQThCOztJQUNsQyxzREFBc0Q7SUFDdERELG1FQUFhQSxDQUFDO1FBQ1pFLGdCQUFnQjtRQUNoQkMsZ0JBQWdCO1FBQ2hCQyxTQUFTLEtBQWlCLHlCQUF5QjtJQUNyRDtJQUVBLDRDQUE0QztJQUM1QyxPQUFPO0FBQ1Q7R0FWTUg7O1FBRUpELCtEQUFhQTs7O0tBRlRDO0FBWU4saUVBQWVBLGlCQUFpQkEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcZmVhdHVyZXNcXHNoYXJlZFxcY29tcG9uZW50c1xcQXV0b0xvZ291dE1hbmFnZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUF1dG9Mb2dvdXQgfSBmcm9tICdAL2hvb2tzL3VzZUF1dG9Mb2dvdXQnO1xuXG4vKipcbiAqIENvbXBvbmVudGUgcXVlIG1hbmVqYSBlbCBhdXRvLWxvZ291dCBwb3IgaW5hY3RpdmlkYWRcbiAqIFNlIGVqZWN1dGEgZW4gdG9kYSBsYSBhcGxpY2FjacOzbiBjdWFuZG8gZWwgdXN1YXJpbyBlc3TDoSBhdXRlbnRpY2Fkb1xuICovXG5jb25zdCBBdXRvTG9nb3V0TWFuYWdlcjogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIC8vIENvbmZpZ3VyYXIgYXV0by1sb2dvdXQgY29uIDUgbWludXRvcyBkZSBpbmFjdGl2aWRhZFxuICB1c2VBdXRvTG9nb3V0KHtcbiAgICB0aW1lb3V0TWludXRlczogNSwgICAgICAgIC8vIDUgbWludXRvcyBkZSBpbmFjdGl2aWRhZFxuICAgIHdhcm5pbmdNaW51dGVzOiAwLjUsICAgICAgLy8gQWR2ZXJ0ZW5jaWEgMzAgc2VndW5kb3MgYW50ZXNcbiAgICBlbmFibGVkOiB0cnVlICAgICAgICAgICAgIC8vIEhhYmlsaXRhZG8gcG9yIGRlZmVjdG9cbiAgfSk7XG5cbiAgLy8gRXN0ZSBjb21wb25lbnRlIG5vIHJlbmRlcml6YSBuYWRhIHZpc2libGVcbiAgcmV0dXJuIG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBBdXRvTG9nb3V0TWFuYWdlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUF1dG9Mb2dvdXQiLCJBdXRvTG9nb3V0TWFuYWdlciIsInRpbWVvdXRNaW51dGVzIiwid2FybmluZ01pbnV0ZXMiLCJlbmFibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/shared/components/AutoLogoutManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(app-pages-browser)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(app-pages-browser)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BackgroundTasksPanel */ \"(app-pages-browser)/./src/features/shared/components/BackgroundTasksPanel.tsx\");\n/* harmony import */ var _AutoLogoutManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AutoLogoutManager */ \"(app-pages-browser)/./src/features/shared/components/AutoLogoutManager.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n // Importar Toaster\nfunction ClientLayout(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.BackgroundTasksProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutoLogoutManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        style: {\n                            background: '#363636',\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = ClientLayout;\nvar _c;\n$RefreshReg$(_c, \"ClientLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/shared/components/ClientLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAutoLogout.ts":
/*!************************************!*\
  !*** ./src/hooks/useAutoLogout.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\nconst useAutoLogout = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { timeoutMinutes = 5, warningMinutes = 0.5, enabled = true } = options;\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const warningTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const warningShownRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convertir minutos a milisegundos\n    const timeoutMs = timeoutMinutes * 60 * 1000;\n    const warningMs = warningMinutes * 60 * 1000;\n    const clearTimeouts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[clearTimeouts]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            if (warningTimeoutRef.current) {\n                clearTimeout(warningTimeoutRef.current);\n                warningTimeoutRef.current = null;\n            }\n        }\n    }[\"useAutoLogout.useCallback[clearTimeouts]\"], []);\n    const handleLogout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleLogout]\": async ()=>{\n            try {\n                await logout();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Sesión cerrada por inactividad', {\n                    duration: 4000,\n                    position: 'top-center'\n                });\n            } catch (error) {\n                console.error('Error al cerrar sesión por inactividad:', error);\n            }\n        }\n    }[\"useAutoLogout.useCallback[handleLogout]\"], [\n        logout\n    ]);\n    const showWarning = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[showWarning]\": ()=>{\n            if (!warningShownRef.current) {\n                warningShownRef.current = true;\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.warning('Tu sesión se cerrará en 30 segundos por inactividad', {\n                    duration: 4000,\n                    position: 'top-center'\n                });\n            }\n        }\n    }[\"useAutoLogout.useCallback[showWarning]\"], []);\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n            if (!enabled || !user) return;\n            lastActivityRef.current = Date.now();\n            warningShownRef.current = false;\n            clearTimeouts();\n            // Configurar timeout para mostrar advertencia\n            if (warningMs > 0) {\n                warningTimeoutRef.current = setTimeout({\n                    \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                        showWarning();\n                    }\n                }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs - warningMs);\n            }\n            // Configurar timeout para logout\n            timeoutRef.current = setTimeout({\n                \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                    handleLogout();\n                }\n            }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs);\n        }\n    }[\"useAutoLogout.useCallback[resetTimer]\"], [\n        enabled,\n        user,\n        timeoutMs,\n        warningMs,\n        clearTimeouts,\n        handleLogout,\n        showWarning\n    ]);\n    // Eventos que indican actividad del usuario\n    const activityEvents = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click'\n    ];\n    const handleActivity = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleActivity]\": (event)=>{\n            // Evitar resetear el timer por movimientos mínimos del mouse\n            if (event.type === 'mousemove') {\n                const now = Date.now();\n                if (now - lastActivityRef.current < 1000) {\n                    return;\n                }\n            }\n            resetTimer();\n        }\n    }[\"useAutoLogout.useCallback[handleActivity]\"], [\n        resetTimer\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            if (!enabled || !user) {\n                clearTimeouts();\n                return;\n            }\n            // Inicializar timer\n            resetTimer();\n            // Agregar event listeners\n            activityEvents.forEach({\n                \"useAutoLogout.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useAutoLogout.useEffect\"]);\n            // Cleanup\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                    activityEvents.forEach({\n                        \"useAutoLogout.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useAutoLogout.useEffect\"]);\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        enabled,\n        user,\n        resetTimer,\n        handleActivity,\n        clearTimeouts\n    ]);\n    // Cleanup al desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        clearTimeouts\n    ]);\n    return {\n        resetTimer,\n        clearTimeouts,\n        isEnabled: enabled && !!user\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoLogout);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBdXRvTG9nb3V0LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1RDtBQUNOO0FBQ1Q7QUFRakMsTUFBTUssZ0JBQWdCO1FBQUNDLDJFQUFnQyxDQUFDO0lBQzdELE1BQU0sRUFDSkMsaUJBQWlCLENBQUMsRUFDbEJDLGlCQUFpQixHQUFHLEVBQ3BCQyxVQUFVLElBQUksRUFDZixHQUFHSDtJQUVKLE1BQU0sRUFBRUksSUFBSSxFQUFFQyxNQUFNLEVBQUUsR0FBR1IsOERBQU9BO0lBQ2hDLE1BQU1TLGFBQWFYLDZDQUFNQSxDQUF3QjtJQUNqRCxNQUFNWSxvQkFBb0JaLDZDQUFNQSxDQUF3QjtJQUN4RCxNQUFNYSxrQkFBa0JiLDZDQUFNQSxDQUFTYyxLQUFLQyxHQUFHO0lBQy9DLE1BQU1DLGtCQUFrQmhCLDZDQUFNQSxDQUFVO0lBRXhDLG1DQUFtQztJQUNuQyxNQUFNaUIsWUFBWVgsaUJBQWlCLEtBQUs7SUFDeEMsTUFBTVksWUFBWVgsaUJBQWlCLEtBQUs7SUFFeEMsTUFBTVksZ0JBQWdCbEIsa0RBQVdBO29EQUFDO1lBQ2hDLElBQUlVLFdBQVdTLE9BQU8sRUFBRTtnQkFDdEJDLGFBQWFWLFdBQVdTLE9BQU87Z0JBQy9CVCxXQUFXUyxPQUFPLEdBQUc7WUFDdkI7WUFDQSxJQUFJUixrQkFBa0JRLE9BQU8sRUFBRTtnQkFDN0JDLGFBQWFULGtCQUFrQlEsT0FBTztnQkFDdENSLGtCQUFrQlEsT0FBTyxHQUFHO1lBQzlCO1FBQ0Y7bURBQUcsRUFBRTtJQUVMLE1BQU1FLGVBQWVyQixrREFBV0E7bURBQUM7WUFDL0IsSUFBSTtnQkFDRixNQUFNUztnQkFDTlAsa0RBQUtBLENBQUNvQixLQUFLLENBQUMsa0NBQWtDO29CQUM1Q0MsVUFBVTtvQkFDVkMsVUFBVTtnQkFDWjtZQUNGLEVBQUUsT0FBT0YsT0FBTztnQkFDZEcsUUFBUUgsS0FBSyxDQUFDLDJDQUEyQ0E7WUFDM0Q7UUFDRjtrREFBRztRQUFDYjtLQUFPO0lBRVgsTUFBTWlCLGNBQWMxQixrREFBV0E7a0RBQUM7WUFDOUIsSUFBSSxDQUFDZSxnQkFBZ0JJLE9BQU8sRUFBRTtnQkFDNUJKLGdCQUFnQkksT0FBTyxHQUFHO2dCQUMxQmpCLGtEQUFLQSxDQUFDeUIsT0FBTyxDQUFDLHVEQUF1RDtvQkFDbkVKLFVBQVU7b0JBQ1ZDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGO2lEQUFHLEVBQUU7SUFFTCxNQUFNSSxhQUFhNUIsa0RBQVdBO2lEQUFDO1lBQzdCLElBQUksQ0FBQ08sV0FBVyxDQUFDQyxNQUFNO1lBRXZCSSxnQkFBZ0JPLE9BQU8sR0FBR04sS0FBS0MsR0FBRztZQUNsQ0MsZ0JBQWdCSSxPQUFPLEdBQUc7WUFDMUJEO1lBRUEsOENBQThDO1lBQzlDLElBQUlELFlBQVksR0FBRztnQkFDakJOLGtCQUFrQlEsT0FBTyxHQUFHVTs2REFBVzt3QkFDckNIO29CQUNGOzREQUFHVixZQUFZQztZQUNqQjtZQUVBLGlDQUFpQztZQUNqQ1AsV0FBV1MsT0FBTyxHQUFHVTt5REFBVztvQkFDOUJSO2dCQUNGO3dEQUFHTDtRQUNMO2dEQUFHO1FBQUNUO1FBQVNDO1FBQU1RO1FBQVdDO1FBQVdDO1FBQWVHO1FBQWNLO0tBQVk7SUFFbEYsNENBQTRDO0lBQzVDLE1BQU1JLGlCQUFpQjtRQUNyQjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELE1BQU1DLGlCQUFpQi9CLGtEQUFXQTtxREFBQyxDQUFDZ0M7WUFDbEMsNkRBQTZEO1lBQzdELElBQUlBLE1BQU1DLElBQUksS0FBSyxhQUFhO2dCQUM5QixNQUFNbkIsTUFBTUQsS0FBS0MsR0FBRztnQkFDcEIsSUFBSUEsTUFBTUYsZ0JBQWdCTyxPQUFPLEdBQUcsTUFBTTtvQkFDeEM7Z0JBQ0Y7WUFDRjtZQUNBUztRQUNGO29EQUFHO1FBQUNBO0tBQVc7SUFFZjlCLGdEQUFTQTttQ0FBQztZQUNSLElBQUksQ0FBQ1MsV0FBVyxDQUFDQyxNQUFNO2dCQUNyQlU7Z0JBQ0E7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQlU7WUFFQSwwQkFBMEI7WUFDMUJFLGVBQWVJLE9BQU87MkNBQUNGLENBQUFBO29CQUNyQkcsU0FBU0MsZ0JBQWdCLENBQUNKLE9BQU9ELGdCQUFnQjtnQkFDbkQ7O1lBRUEsVUFBVTtZQUNWOzJDQUFPO29CQUNMYjtvQkFDQVksZUFBZUksT0FBTzttREFBQ0YsQ0FBQUE7NEJBQ3JCRyxTQUFTRSxtQkFBbUIsQ0FBQ0wsT0FBT0QsZ0JBQWdCO3dCQUN0RDs7Z0JBQ0Y7O1FBQ0Y7a0NBQUc7UUFBQ3hCO1FBQVNDO1FBQU1vQjtRQUFZRztRQUFnQmI7S0FBYztJQUU3RCx1QkFBdUI7SUFDdkJwQixnREFBU0E7bUNBQUM7WUFDUjsyQ0FBTztvQkFDTG9CO2dCQUNGOztRQUNGO2tDQUFHO1FBQUNBO0tBQWM7SUFFbEIsT0FBTztRQUNMVTtRQUNBVjtRQUNBb0IsV0FBVy9CLFdBQVcsQ0FBQyxDQUFDQztJQUMxQjtBQUNGLEVBQUU7QUFFRixpRUFBZUwsYUFBYUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcaG9va3NcXHVzZUF1dG9Mb2dvdXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuXG5pbnRlcmZhY2UgVXNlQXV0b0xvZ291dE9wdGlvbnMge1xuICB0aW1lb3V0TWludXRlcz86IG51bWJlcjtcbiAgd2FybmluZ01pbnV0ZXM/OiBudW1iZXI7XG4gIGVuYWJsZWQ/OiBib29sZWFuO1xufVxuXG5leHBvcnQgY29uc3QgdXNlQXV0b0xvZ291dCA9IChvcHRpb25zOiBVc2VBdXRvTG9nb3V0T3B0aW9ucyA9IHt9KSA9PiB7XG4gIGNvbnN0IHtcbiAgICB0aW1lb3V0TWludXRlcyA9IDUsXG4gICAgd2FybmluZ01pbnV0ZXMgPSAwLjUsIC8vIDMwIHNlZ3VuZG9zIGFudGVzXG4gICAgZW5hYmxlZCA9IHRydWVcbiAgfSA9IG9wdGlvbnM7XG5cbiAgY29uc3QgeyB1c2VyLCBsb2dvdXQgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgdGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xuICBjb25zdCB3YXJuaW5nVGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBsYXN0QWN0aXZpdHlSZWYgPSB1c2VSZWY8bnVtYmVyPihEYXRlLm5vdygpKTtcbiAgY29uc3Qgd2FybmluZ1Nob3duUmVmID0gdXNlUmVmPGJvb2xlYW4+KGZhbHNlKTtcblxuICAvLyBDb252ZXJ0aXIgbWludXRvcyBhIG1pbGlzZWd1bmRvc1xuICBjb25zdCB0aW1lb3V0TXMgPSB0aW1lb3V0TWludXRlcyAqIDYwICogMTAwMDtcbiAgY29uc3Qgd2FybmluZ01zID0gd2FybmluZ01pbnV0ZXMgKiA2MCAqIDEwMDA7XG5cbiAgY29uc3QgY2xlYXJUaW1lb3V0cyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAodGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dFJlZi5jdXJyZW50KTtcbiAgICAgIHRpbWVvdXRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICAgIGlmICh3YXJuaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQod2FybmluZ1RpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgICB3YXJuaW5nVGltZW91dFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGxvZ291dCgpO1xuICAgICAgdG9hc3QuZXJyb3IoJ1Nlc2nDs24gY2VycmFkYSBwb3IgaW5hY3RpdmlkYWQnLCB7XG4gICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICBwb3NpdGlvbjogJ3RvcC1jZW50ZXInXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgY2VycmFyIHNlc2nDs24gcG9yIGluYWN0aXZpZGFkOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtsb2dvdXRdKTtcblxuICBjb25zdCBzaG93V2FybmluZyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoIXdhcm5pbmdTaG93blJlZi5jdXJyZW50KSB7XG4gICAgICB3YXJuaW5nU2hvd25SZWYuY3VycmVudCA9IHRydWU7XG4gICAgICB0b2FzdC53YXJuaW5nKCdUdSBzZXNpw7NuIHNlIGNlcnJhcsOhIGVuIDMwIHNlZ3VuZG9zIHBvciBpbmFjdGl2aWRhZCcsIHtcbiAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgIHBvc2l0aW9uOiAndG9wLWNlbnRlcidcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IHJlc2V0VGltZXIgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFlbmFibGVkIHx8ICF1c2VyKSByZXR1cm47XG5cbiAgICBsYXN0QWN0aXZpdHlSZWYuY3VycmVudCA9IERhdGUubm93KCk7XG4gICAgd2FybmluZ1Nob3duUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICBjbGVhclRpbWVvdXRzKCk7XG5cbiAgICAvLyBDb25maWd1cmFyIHRpbWVvdXQgcGFyYSBtb3N0cmFyIGFkdmVydGVuY2lhXG4gICAgaWYgKHdhcm5pbmdNcyA+IDApIHtcbiAgICAgIHdhcm5pbmdUaW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2hvd1dhcm5pbmcoKTtcbiAgICAgIH0sIHRpbWVvdXRNcyAtIHdhcm5pbmdNcyk7XG4gICAgfVxuXG4gICAgLy8gQ29uZmlndXJhciB0aW1lb3V0IHBhcmEgbG9nb3V0XG4gICAgdGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBoYW5kbGVMb2dvdXQoKTtcbiAgICB9LCB0aW1lb3V0TXMpO1xuICB9LCBbZW5hYmxlZCwgdXNlciwgdGltZW91dE1zLCB3YXJuaW5nTXMsIGNsZWFyVGltZW91dHMsIGhhbmRsZUxvZ291dCwgc2hvd1dhcm5pbmddKTtcblxuICAvLyBFdmVudG9zIHF1ZSBpbmRpY2FuIGFjdGl2aWRhZCBkZWwgdXN1YXJpb1xuICBjb25zdCBhY3Rpdml0eUV2ZW50cyA9IFtcbiAgICAnbW91c2Vkb3duJyxcbiAgICAnbW91c2Vtb3ZlJyxcbiAgICAna2V5cHJlc3MnLFxuICAgICdzY3JvbGwnLFxuICAgICd0b3VjaHN0YXJ0JyxcbiAgICAnY2xpY2snXG4gIF07XG5cbiAgY29uc3QgaGFuZGxlQWN0aXZpdHkgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IEV2ZW50KSA9PiB7XG4gICAgLy8gRXZpdGFyIHJlc2V0ZWFyIGVsIHRpbWVyIHBvciBtb3ZpbWllbnRvcyBtw61uaW1vcyBkZWwgbW91c2VcbiAgICBpZiAoZXZlbnQudHlwZSA9PT0gJ21vdXNlbW92ZScpIHtcbiAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgICBpZiAobm93IC0gbGFzdEFjdGl2aXR5UmVmLmN1cnJlbnQgPCAxMDAwKSB7IC8vIFRocm90dGxlIGRlIDEgc2VndW5kb1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgfVxuICAgIHJlc2V0VGltZXIoKTtcbiAgfSwgW3Jlc2V0VGltZXJdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZW5hYmxlZCB8fCAhdXNlcikge1xuICAgICAgY2xlYXJUaW1lb3V0cygpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIEluaWNpYWxpemFyIHRpbWVyXG4gICAgcmVzZXRUaW1lcigpO1xuXG4gICAgLy8gQWdyZWdhciBldmVudCBsaXN0ZW5lcnNcbiAgICBhY3Rpdml0eUV2ZW50cy5mb3JFYWNoKGV2ZW50ID0+IHtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoZXZlbnQsIGhhbmRsZUFjdGl2aXR5LCB0cnVlKTtcbiAgICB9KTtcblxuICAgIC8vIENsZWFudXBcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJUaW1lb3V0cygpO1xuICAgICAgYWN0aXZpdHlFdmVudHMuZm9yRWFjaChldmVudCA9PiB7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoZXZlbnQsIGhhbmRsZUFjdGl2aXR5LCB0cnVlKTtcbiAgICAgIH0pO1xuICAgIH07XG4gIH0sIFtlbmFibGVkLCB1c2VyLCByZXNldFRpbWVyLCBoYW5kbGVBY3Rpdml0eSwgY2xlYXJUaW1lb3V0c10pO1xuXG4gIC8vIENsZWFudXAgYWwgZGVzbW9udGFyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNsZWFyVGltZW91dHMoKTtcbiAgICB9O1xuICB9LCBbY2xlYXJUaW1lb3V0c10pO1xuXG4gIHJldHVybiB7XG4gICAgcmVzZXRUaW1lcixcbiAgICBjbGVhclRpbWVvdXRzLFxuICAgIGlzRW5hYmxlZDogZW5hYmxlZCAmJiAhIXVzZXJcbiAgfTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IHVzZUF1dG9Mb2dvdXQ7XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ2FsbGJhY2siLCJ1c2VBdXRoIiwidG9hc3QiLCJ1c2VBdXRvTG9nb3V0Iiwib3B0aW9ucyIsInRpbWVvdXRNaW51dGVzIiwid2FybmluZ01pbnV0ZXMiLCJlbmFibGVkIiwidXNlciIsImxvZ291dCIsInRpbWVvdXRSZWYiLCJ3YXJuaW5nVGltZW91dFJlZiIsImxhc3RBY3Rpdml0eVJlZiIsIkRhdGUiLCJub3ciLCJ3YXJuaW5nU2hvd25SZWYiLCJ0aW1lb3V0TXMiLCJ3YXJuaW5nTXMiLCJjbGVhclRpbWVvdXRzIiwiY3VycmVudCIsImNsZWFyVGltZW91dCIsImhhbmRsZUxvZ291dCIsImVycm9yIiwiZHVyYXRpb24iLCJwb3NpdGlvbiIsImNvbnNvbGUiLCJzaG93V2FybmluZyIsIndhcm5pbmciLCJyZXNldFRpbWVyIiwic2V0VGltZW91dCIsImFjdGl2aXR5RXZlbnRzIiwiaGFuZGxlQWN0aXZpdHkiLCJldmVudCIsInR5cGUiLCJmb3JFYWNoIiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImlzRW5hYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoLogout.ts\n"));

/***/ })

});