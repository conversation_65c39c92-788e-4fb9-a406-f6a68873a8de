"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards\n        .eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            return [];\n        }\n        // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]\n        // We need to transform this to a direct property numero_flashcards\n        return (data === null || data === void 0 ? void 0 : data.map((coleccion)=>({\n                ...coleccion,\n                // @ts-ignore Supabase types might not be perfect here for related counts\n                numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0 ? coleccion.flashcards[0].count : 0\n            }))) || [];\n    } catch (error) {\n        console.error('Error al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, fecha').in('flashcard_id', flashcardIds).order('fecha', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión mejorada para evitar errores 409\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('Usuario no autenticado');\n            return false;\n        }\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        let progresoExiste = false;\n        // Intentar obtener progreso existente\n        const { data: progresoExistente, error: errorConsulta } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).single();\n        if (!errorConsulta && progresoExistente) {\n            factorFacilidad = progresoExistente.factor_facilidad || 2.5;\n            intervalo = progresoExistente.intervalo || 1;\n            repeticiones = progresoExistente.repeticiones || 0;\n            estado = progresoExistente.estado || 'nuevo';\n            progresoExiste = true;\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso usando insert o update según corresponda\n        let errorProgreso = null;\n        if (progresoExiste) {\n            // Actualizar progreso existente\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            }).eq('flashcard_id', flashcardId);\n            errorProgreso = error;\n        } else {\n            // Crear nuevo progreso\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').insert({\n                flashcard_id: flashcardId,\n                user_id: user.id,\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            });\n            errorProgreso = error;\n        }\n        if (errorProgreso) {\n            console.error('Error al guardar progreso:', errorProgreso);\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            user_id: user.id,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ })

});