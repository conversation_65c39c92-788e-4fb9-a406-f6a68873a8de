import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>X,
  FiHelpCircle,
  FiClock,
  <PERSON>Book<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FiRefreshCw
} from 'react-icons/fi';
import {
  ColeccionFlashcards,
  DificultadRespuesta,
  obtenerColeccionesFlashcards,
  obtenerFlashcardsParaEstudiar,
  obtenerFlashcardsMasDificiles,
  obtenerFlashcardsAleatorias,
  obtenerFlashcardsNoRecientes,
  obtenerFlashcardsPorEstado,
  registrarRespuestaFlashcard,
  reiniciarProgresoFlashcard,
  obtenerEstadisticasColeccion,
  FlashcardConProgreso
} from '../lib/supabase';
import StudyStatistics from './StudyStatistics';
import RevisionHistory from './RevisionHistory';

// Tipos
type EstadoFlashcard = 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido';

interface ProgresoFlashcard {
  estado: EstadoFlashcard;
  siguienteRepaso: string;
  factorFacilidad: number;
  repeticiones: number;
  intervalo: number;
}

// Tipos para los botones de dificultad
type DificultadButton = {
  tipo: DificultadRespuesta;
  label: string;
  color: string;
  bgColor: string;
  hoverBgColor: string;
  icon: React.ReactNode;
};

interface EstadisticasColeccion {
  total: number;
  nuevas: number;
  aprendiendo: number;
  repasando: number;
  aprendidas: number;
  paraHoy: number;
}

// Tipos para opciones de estudio adicionales
type TipoEstudio = 'programadas' | 'dificiles' | 'aleatorias' | 'no-recientes' | 'nuevas' | 'aprendiendo' | 'repasando' | 'aprendidas';

interface OpcionEstudio {
  tipo: TipoEstudio;
  label: string;
  descripcion: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  hoverBgColor: string;
}

// Componente para mostrar una tarjeta de colección
const ColeccionCard: React.FC<{
  coleccion: ColeccionFlashcards;
  isSelected: boolean;
  onClick: () => void;
  estadisticas?: EstadisticasColeccion | null;
}> = ({ coleccion, isSelected, onClick, estadisticas }) => (
  <div
    onClick={onClick}
    className={`p-4 border rounded-xl cursor-pointer transition-all duration-200 ${
      isSelected
        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50/50'
    }`}
  >
    <div className="flex justify-between items-start">
      <h3 className="font-semibold text-gray-900">{coleccion.titulo}</h3>
      {estadisticas && (
        <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
          {estadisticas.paraHoy} para hoy
        </span>
      )}
    </div>

    {coleccion.descripcion && (
      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
        {coleccion.descripcion}
      </p>
    )}

    {estadisticas && (
      <div className="mt-3 pt-2 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
        <span className="flex items-center">
          <FiBookOpen className="mr-1" size={12} />
          {estadisticas.total} tarjetas
        </span>
        <div className="flex space-x-1">
          <span className="text-green-600">{estadisticas.aprendidas}✓</span>
          <span className="text-yellow-500">{estadisticas.aprendiendo}⏳</span>
          <span className="text-gray-400">{estadisticas.nuevas}+</span>
        </div>
      </div>
    )}
  </div>
);

// Componente para los botones de dificultad
const DificultadButton: React.FC<{
  tipo: DificultadRespuesta;
  label: string;
  color: string;
  bgColor: string;
  hoverBgColor: string;
  icon: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
}> = ({ tipo, label, color, bgColor, hoverBgColor, icon, onClick, disabled = false }) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`flex-1 flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 ${
      disabled ? 'opacity-50 cursor-not-allowed' : `${hoverBgColor} hover:shadow-md`
    } ${bgColor} text-${color}`}
  >
    <span className="text-xl mb-1">{icon}</span>
    <span className="text-sm font-medium">{label}</span>
  </button>
);

export default function FlashcardViewer() {
  // Estados principales
  const [colecciones, setColecciones] = useState<ColeccionFlashcards[]>([]);
  const [coleccionSeleccionada, setColeccionSeleccionada] = useState<ColeccionFlashcards | null>(null);
  const [flashcards, setFlashcards] = useState<FlashcardConProgreso[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingColecciones, setIsLoadingColecciones] = useState(true);
  const [error, setError] = useState('');
  const [activeIndex, setActiveIndex] = useState(0);
  const [mostrarRespuesta, setMostrarRespuesta] = useState(false);
  const [respondiendo, setRespondiendo] = useState(false);
  const [estadisticas, setEstadisticas] = useState<EstadisticasColeccion | null>(null);
  const [mostrarHistorial, setMostrarHistorial] = useState(false);
  const [mostrarEstadisticas, setMostrarEstadisticas] = useState(false);
  const [modoEstudio, setModoEstudio] = useState(false);
  const [tipoEstudioActual, setTipoEstudioActual] = useState<TipoEstudio>('programadas');
  const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = useState(false);

  // Configuración de los botones de dificultad
  const dificultadButtons: DificultadButton[] = [
    {
      tipo: 'dificil',
      label: 'Difícil',
      color: 'red-600',
      bgColor: 'bg-red-100',
      hoverBgColor: 'hover:bg-red-200',
      icon: <FiHelpCircle className="text-red-600" />
    },
    {
      tipo: 'normal',
      label: 'Bien',
      color: 'yellow-600',
      bgColor: 'bg-yellow-100',
      hoverBgColor: 'hover:bg-yellow-200',
      icon: <FiCheck className="text-yellow-600" />
    },
    {
      tipo: 'facil',
      label: 'Fácil',
      color: 'green-600',
      bgColor: 'bg-green-100',
      hoverBgColor: 'hover:bg-green-200',
      icon: <FiCheck className="text-green-600" />
    }
  ];

  // Configuración de las opciones de estudio
  const opcionesEstudio: OpcionEstudio[] = [
    {
      tipo: 'programadas',
      label: 'Programadas para hoy',
      descripcion: 'Tarjetas que deben estudiarse según el algoritmo de repetición espaciada',
      icon: <FiClock className="text-blue-600" />,
      color: 'blue-600',
      bgColor: 'bg-blue-100',
      hoverBgColor: 'hover:bg-blue-200'
    },
    {
      tipo: 'dificiles',
      label: 'Más difíciles',
      descripcion: 'Tarjetas que has marcado como difíciles más frecuentemente',
      icon: <FiHelpCircle className="text-red-600" />,
      color: 'red-600',
      bgColor: 'bg-red-100',
      hoverBgColor: 'hover:bg-red-200'
    },
    {
      tipo: 'aleatorias',
      label: 'Aleatorias',
      descripcion: 'Selección aleatoria de tarjetas de la colección',
      icon: <FiRefreshCw className="text-purple-600" />,
      color: 'purple-600',
      bgColor: 'bg-purple-100',
      hoverBgColor: 'hover:bg-purple-200'
    },
    {
      tipo: 'no-recientes',
      label: 'No estudiadas recientemente',
      descripcion: 'Tarjetas que no has revisado en mucho tiempo',
      icon: <FiClock className="text-orange-600" />,
      color: 'orange-600',
      bgColor: 'bg-orange-100',
      hoverBgColor: 'hover:bg-orange-200'
    },
    {
      tipo: 'nuevas',
      label: 'Nuevas',
      descripcion: 'Tarjetas que nunca has estudiado',
      icon: <FiBookOpen className="text-green-600" />,
      color: 'green-600',
      bgColor: 'bg-green-100',
      hoverBgColor: 'hover:bg-green-200'
    },
    {
      tipo: 'aprendiendo',
      label: 'En aprendizaje',
      descripcion: 'Tarjetas que estás aprendiendo actualmente',
      icon: <FiLoader className="text-yellow-600" />,
      color: 'yellow-600',
      bgColor: 'bg-yellow-100',
      hoverBgColor: 'hover:bg-yellow-200'
    }
  ];

  useEffect(() => {
    const cargarColecciones = async () => {
      setIsLoading(true);
      try {
        const data = await obtenerColeccionesFlashcards();
        setColecciones(data);
      } catch (error) {
        console.error('Error al cargar colecciones:', error);
        setError('No se pudieron cargar las colecciones de flashcards');
      } finally {
        setIsLoading(false);
      }
    };

    cargarColecciones();
  }, []);

  const handleSeleccionarColeccion = async (coleccion: ColeccionFlashcards) => {
    setIsLoading(true);
    setError('');
    setColeccionSeleccionada(coleccion);
    setActiveIndex(0);
    setMostrarRespuesta(false);
    setRespondiendo(false);
    setModoEstudio(false);

    try {
      // Cargar flashcards con su progreso
      const data = await obtenerFlashcardsParaEstudiar(coleccion.id) as FlashcardConProgreso[];

      // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto
      const ordenadas = [...data].sort((a, b) => {
        if (a.debeEstudiar && !b.debeEstudiar) return -1;
        if (!a.debeEstudiar && b.debeEstudiar) return 1;
        return 0;
      });

      setFlashcards(ordenadas);

      // Cargar estadísticas
      const stats = await obtenerEstadisticasColeccion(coleccion.id);
      setEstadisticas(stats);
    } catch (error) {
      console.error('Error al cargar flashcards:', error);
      setError('No se pudieron cargar las flashcards de esta colección');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNextCard = () => {
    if (activeIndex < flashcards.length - 1) {
      setActiveIndex(activeIndex + 1);
      setMostrarRespuesta(false);
      setRespondiendo(false);
    }
  };

  const handlePrevCard = () => {
    if (activeIndex > 0) {
      setActiveIndex(activeIndex - 1);
      setMostrarRespuesta(false);
      setRespondiendo(false);
    }
  };

  const toggleRespuesta = () => {
    setMostrarRespuesta(!mostrarRespuesta);
    // No establecemos respondiendo a true aquí, ahora tenemos un botón específico para eso
  };

  const iniciarModoEstudio = async (tipoEstudio: TipoEstudio = 'programadas') => {
    setIsLoading(true);

    try {
      if (coleccionSeleccionada) {
        let flashcardsParaEstudiar: FlashcardConProgreso[] = [];

        // Obtener flashcards según el tipo de estudio seleccionado
        switch (tipoEstudio) {
          case 'programadas':
            const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
            flashcardsParaEstudiar = data.filter(flashcard => flashcard.debeEstudiar);
            break;
          case 'dificiles':
            flashcardsParaEstudiar = await obtenerFlashcardsMasDificiles(coleccionSeleccionada.id, 20);
            break;
          case 'aleatorias':
            flashcardsParaEstudiar = await obtenerFlashcardsAleatorias(coleccionSeleccionada.id, 20);
            break;
          case 'no-recientes':
            flashcardsParaEstudiar = await obtenerFlashcardsNoRecientes(coleccionSeleccionada.id, 20);
            break;
          case 'nuevas':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'nuevo', 20);
            break;
          case 'aprendiendo':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'aprendiendo', 20);
            break;
          case 'repasando':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'repasando', 20);
            break;
          case 'aprendidas':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'aprendido', 20);
            break;
          default:
            const defaultData = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
            flashcardsParaEstudiar = defaultData.filter(flashcard => flashcard.debeEstudiar);
        }

        // Actualizar estadísticas
        const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
        setEstadisticas(stats);

        // Si no hay flashcards para el tipo seleccionado, mostrar mensaje
        if (flashcardsParaEstudiar.length === 0) {
          if (tipoEstudio === 'programadas') {
            alert('No hay flashcards programadas para estudiar hoy. Puedes usar "Opciones de estudio" para elegir otro tipo de repaso.');
            return;
          } else {
            alert(`No hay flashcards disponibles para el tipo de estudio "${opcionesEstudio.find(o => o.tipo === tipoEstudio)?.label}".`);
            return;
          }
        }

        // Usar las flashcards obtenidas
        setFlashcards(flashcardsParaEstudiar);
        setTipoEstudioActual(tipoEstudio);

        // Iniciar el modo de estudio
        setModoEstudio(true);
        setActiveIndex(0);
        setMostrarRespuesta(false);
        setRespondiendo(false);
        setMostrarOpcionesEstudio(false);
      }
    } catch (error) {
      console.error('Error al iniciar modo estudio:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const salirModoEstudio = () => {
    setModoEstudio(false);
    setMostrarRespuesta(false);
    setRespondiendo(false);
  };

  const handleRespuesta = async (dificultad: DificultadRespuesta) => {
    if (!flashcards[activeIndex]) return;

    setIsLoading(true);

    try {
      // Registrar la respuesta (esto maneja tanto el progreso como el historial)
      const exito = await registrarRespuestaFlashcard(flashcards[activeIndex].id, dificultad);

      if (!exito) {
        throw new Error('Error al registrar la respuesta');
      }

      // Recargar las flashcards y estadísticas si estamos en la última tarjeta
      if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {
        // Para estudios programados, filtrar solo las que deben estudiarse
        if (tipoEstudioActual === 'programadas') {
          const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id) as FlashcardConProgreso[];
          const flashcardsParaEstudiar = data.filter(flashcard => flashcard.debeEstudiar);

          // Actualizar estadísticas
          const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
          setEstadisticas(stats);

          if (flashcardsParaEstudiar.length > 0) {
            setFlashcards(flashcardsParaEstudiar);
            setActiveIndex(0);
          } else {
            // Si no hay más flashcards para hoy, mostrar mensaje y salir
            alert('¡Has completado todas las flashcards programadas para hoy! Puedes usar "Opciones de estudio" para continuar con otros tipos de repaso.');
            setModoEstudio(false);

            // Recargar todas las flashcards para mostrar en la vista principal
            const todasLasFlashcards = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
            const ordenadas = [...todasLasFlashcards].sort((a, b) => {
              if (a.debeEstudiar && !b.debeEstudiar) return -1;
              if (!a.debeEstudiar && b.debeEstudiar) return 1;
              return 0;
            });
            setFlashcards(ordenadas);
          }
        } else {
          // Para otros tipos de estudio, simplemente terminar la sesión
          alert(`¡Has completado la sesión de estudio de "${opcionesEstudio.find(o => o.tipo === tipoEstudioActual)?.label}"!`);
          setModoEstudio(false);

          // Recargar todas las flashcards
          const todasLasFlashcards = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
          const ordenadas = [...todasLasFlashcards].sort((a, b) => {
            if (a.debeEstudiar && !b.debeEstudiar) return -1;
            if (!a.debeEstudiar && b.debeEstudiar) return 1;
            return 0;
          });
          setFlashcards(ordenadas);

          // Actualizar estadísticas
          const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
          setEstadisticas(stats);
        }
      } else {
        // Avanzar a la siguiente tarjeta
        setActiveIndex(activeIndex + 1);
      }

      // Resetear el estado
      setMostrarRespuesta(false);
      setRespondiendo(false);
    } catch (error) {
      console.error('Error al actualizar progreso:', error);
      setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReiniciarProgreso = async () => {
    if (!flashcards[activeIndex]) return;

    setIsLoading(true);

    try {
      // Reiniciar el progreso de la flashcard actual
      await reiniciarProgresoFlashcard(flashcards[activeIndex].id);

      // Recargar las flashcards y estadísticas
      if (coleccionSeleccionada) {
        const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
        setFlashcards(data);

        const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
        setEstadisticas(stats);
      }

      // Resetear el estado
      setMostrarRespuesta(false);
      setRespondiendo(false);
    } catch (error) {
      console.error('Error al reiniciar progreso:', error);
      setError('Error al reiniciar el progreso. Por favor, inténtalo de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  // Formatear la fecha para mostrarla en la lista
  const formatearFecha = (fechaStr: string): string => {
    const fecha = new Date(fechaStr);
    return fecha.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="mt-8 border-t pt-8">
      <h2 className="text-xl font-bold mb-4">Mis Flashcards</h2>

      {isLoading && !coleccionSeleccionada ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Cargando colecciones...</p>
        </div>
      ) : error ? (
        <div className="text-red-500 text-sm py-2">{error}</div>
      ) : colecciones.length === 0 ? (
        <div className="text-gray-500 text-sm py-2">No hay colecciones de flashcards guardadas</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {!modoEstudio && (
            <div className="md:col-span-2 mb-4">
              <h3 className="text-lg font-semibold mb-2">Selecciona una colección</h3>
            </div>
          )}

          {!modoEstudio ? (
            // Lista de colecciones
            colecciones.map((coleccion) => (
              <div
                key={coleccion.id}
                className={`p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                  coleccionSeleccionada?.id === coleccion.id ? 'border-orange-500 bg-orange-50' : ''
                }`}
                onClick={() => handleSeleccionarColeccion(coleccion)}
              >
                <div className="font-medium text-lg">{coleccion.titulo}</div>
                {coleccion.descripcion && (
                  <p className="text-gray-600 text-sm mt-1">{coleccion.descripcion}</p>
                )}
                <div className="text-xs text-gray-500 mt-2">
                  Creada el {formatearFecha(coleccion.creado_en)}
                </div>
              </div>
            ))
          ) : (
            // Modo estudio
            <div className="md:col-span-2">
              <div className="flex justify-between items-center mb-4">
                <button
                  onClick={salirModoEstudio}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded inline-flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                  Volver a colecciones
                </button>
                <span className="text-gray-600">
                  {activeIndex + 1} de {flashcards.length}
                </span>
              </div>

              <div className="bg-white border rounded-lg shadow-md p-6 mb-4">
                <div className="flex justify-between items-center mb-4">
                  <button
                    onClick={handlePrevCard}
                    disabled={activeIndex === 0}
                    className={`p-2 rounded-full ${
                      activeIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <div className="text-center">
                    <h3 className="text-lg font-semibold">{coleccionSeleccionada?.titulo}</h3>
                    <div className="text-xs text-gray-500 mt-1">
                      <span className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full mr-2">
                        {opcionesEstudio.find(o => o.tipo === tipoEstudioActual)?.label || 'Estudio'}
                      </span>
                      {flashcards[activeIndex]?.progreso && (
                        <span className={`px-2 py-0.5 rounded-full ${
                          flashcards[activeIndex].progreso?.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' :
                          flashcards[activeIndex].progreso?.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' :
                          flashcards[activeIndex].progreso?.estado === 'repasando' ? 'bg-orange-100 text-orange-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {flashcards[activeIndex].progreso?.estado}
                        </span>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={handleNextCard}
                    disabled={activeIndex === flashcards.length - 1 || mostrarRespuesta}
                    className={`p-2 rounded-full ${
                      activeIndex === flashcards.length - 1 || mostrarRespuesta ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>

                <div
                  className="min-h-[200px] flex items-center justify-center cursor-pointer"
                  onClick={!mostrarRespuesta ? toggleRespuesta : undefined}
                >
                  <div className="text-center p-4 w-full">
                    {!mostrarRespuesta ? (
                      <div className="font-semibold text-lg">{flashcards[activeIndex]?.pregunta}</div>
                    ) : (
                      <div>
                        <div className="font-semibold text-lg mb-2">{flashcards[activeIndex]?.pregunta}</div>
                        <div className="border-t pt-4 text-left whitespace-pre-wrap">{flashcards[activeIndex]?.respuesta}</div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-4 text-center">
                  {!mostrarRespuesta ? (
                    <button
                      onClick={toggleRespuesta}
                      className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                    >
                      Mostrar respuesta
                    </button>
                  ) : isLoading ? (
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-500 mr-2"></div>
                      <span>Actualizando progreso...</span>
                    </div>
                  ) : respondiendo ? (
                    <div className="space-y-4">
                      <div className="flex justify-center space-x-2">
                        <button
                          onClick={() => handleRespuesta('dificil')}
                          className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                          disabled={isLoading}
                        >
                          Difícil
                        </button>
                        <button
                          onClick={() => handleRespuesta('normal')}
                          className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                          disabled={isLoading}
                        >
                          Normal
                        </button>
                        <button
                          onClick={() => handleRespuesta('facil')}
                          className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                          disabled={isLoading}
                        >
                          Fácil
                        </button>
                      </div>
                      <div className="flex justify-center space-x-4 text-sm">
                        <button
                          onClick={handleReiniciarProgreso}
                          className="text-gray-600 hover:text-gray-800 underline"
                          disabled={isLoading}
                        >
                          Reiniciar progreso
                        </button>
                        <button
                          onClick={() => setMostrarHistorial(true)}
                          className="text-blue-600 hover:text-blue-800 underline"
                          disabled={isLoading}
                        >
                          Ver historial
                        </button>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={() => setRespondiendo(true)}
                      className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                    >
                      Evaluar conocimiento
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {coleccionSeleccionada && flashcards.length > 0 && !modoEstudio && (
            <div className="md:col-span-2 mt-4">
              <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-4 gap-4">
                <div>
                  <h3 className="text-lg font-semibold">{coleccionSeleccionada.titulo}</h3>
                  {estadisticas && (
                    <div className="text-sm text-gray-600 mt-1">
                      <p>Total: {estadisticas.total} tarjetas</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                          Nuevas: {estadisticas.nuevas}
                        </span>
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                          Aprendiendo: {estadisticas.aprendiendo}
                        </span>
                        <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">
                          Repasando: {estadisticas.repasando}
                        </span>
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                          Aprendidas: {estadisticas.aprendidas}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                  <button
                    onClick={() => iniciarModoEstudio('programadas')}
                    className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    {`Estudiar (${estadisticas ? estadisticas.paraHoy : 0} para hoy)`}
                  </button>
                  <button
                    onClick={() => setMostrarOpcionesEstudio(true)}
                    className="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Opciones de estudio
                  </button>
                  <button
                    onClick={() => setMostrarEstadisticas(true)}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  >
                    Ver estadísticas
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {flashcards.map((card, index) => (
                  <div
                    key={card.id}
                    className={`p-3 border rounded-lg ${
                      card.debeEstudiar ? 'border-orange-300 bg-orange-50' : ''
                    }`}
                  >
                    <p className="font-medium">{card.pregunta}</p>
                    <div className="flex justify-between items-center mt-2">
                      <p className="text-xs text-gray-500">Tarjeta {index + 1}</p>
                      {card.progreso && (
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          card.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' :
                          card.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' :
                          card.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {card.progreso.estado}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Modal de historial de revisiones */}
      {mostrarHistorial && flashcards[activeIndex] && (
        <RevisionHistory
          flashcardId={flashcards[activeIndex].id}
          onClose={() => setMostrarHistorial(false)}
        />
      )}

      {/* Modal de estadísticas detalladas */}
      {mostrarEstadisticas && coleccionSeleccionada && (
        <StudyStatistics
          coleccionId={coleccionSeleccionada.id}
          onClose={() => setMostrarEstadisticas(false)}
        />
      )}

      {/* Modal de opciones de estudio adicionales */}
      {mostrarOpcionesEstudio && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Opciones de Estudio</h3>
              <button
                onClick={() => setMostrarOpcionesEstudio(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FiX size={24} />
              </button>
            </div>

            <p className="text-gray-600 mb-6">
              Elige el tipo de estudio que prefieras. Cada opción te permitirá enfocar tu aprendizaje de manera diferente:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {opcionesEstudio.map((opcion) => (
                <button
                  key={opcion.tipo}
                  onClick={() => iniciarModoEstudio(opcion.tipo)}
                  className={`p-4 border rounded-lg text-left transition-all duration-200 ${opcion.hoverBgColor} ${opcion.bgColor} border-gray-200 hover:border-gray-300`}
                  disabled={isLoading}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {opcion.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-medium text-${opcion.color} mb-1`}>
                        {opcion.tipo === 'programadas'
                          ? `${opcion.label} (${estadisticas ? estadisticas.paraHoy : 0})`
                          : opcion.label
                        }
                      </h4>
                      <p className="text-sm text-gray-600">
                        {opcion.descripcion}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setMostrarOpcionesEstudio(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
