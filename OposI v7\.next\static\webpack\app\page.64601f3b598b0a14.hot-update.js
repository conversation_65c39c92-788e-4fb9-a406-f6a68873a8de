"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards\n        .eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            return [];\n        }\n        // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]\n        // We need to transform this to a direct property numero_flashcards\n        return (data === null || data === void 0 ? void 0 : data.map((coleccion)=>({\n                ...coleccion,\n                // @ts-ignore Supabase types might not be perfect here for related counts\n                numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0 ? coleccion.flashcards[0].count : 0\n            }))) || [];\n    } catch (error) {\n        console.error('Error al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').select('flashcard_id, fecha').in('flashcard_id', flashcardIds).order('fecha', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión mejorada para evitar errores 409\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('Usuario no autenticado');\n            return false;\n        }\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        let progresoExiste = false;\n        // Intentar obtener progreso existente\n        const { data: progresoExistente, error: errorConsulta } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).single();\n        if (!errorConsulta && progresoExistente) {\n            factorFacilidad = progresoExistente.factor_facilidad || 2.5;\n            intervalo = progresoExistente.intervalo || 1;\n            repeticiones = progresoExistente.repeticiones || 0;\n            estado = progresoExistente.estado || 'nuevo';\n            progresoExiste = true;\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso usando insert o update según corresponda\n        let errorProgreso = null;\n        if (progresoExiste) {\n            // Actualizar progreso existente\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            }).eq('flashcard_id', flashcardId);\n            errorProgreso = error;\n        } else {\n            // Crear nuevo progreso\n            const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').insert({\n                flashcard_id: flashcardId,\n                factor_facilidad: nuevoFactorFacilidad,\n                intervalo: nuevoIntervalo,\n                repeticiones: nuevasRepeticiones,\n                estado: nuevoEstado,\n                ultima_revision: ahora.toISOString(),\n                proxima_revision: proximaRevision.toISOString()\n            });\n            errorProgreso = error;\n        }\n        if (errorProgreso) {\n            console.error('Error al guardar progreso:', errorProgreso);\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            user_id: user.id,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ })

});