"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiCheck,FiCheckSquare,FiChevronRight,FiFileText,FiLayers,FiList,FiLogOut,FiMessageSquare,FiRefreshCw,FiSettings,FiUpload!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/DocumentSelector */ \"(app-pages-browser)/./src/components/DocumentSelector.tsx\");\n/* harmony import */ var _components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/QuestionForm */ \"(app-pages-browser)/./src/components/QuestionForm.tsx\");\n/* harmony import */ var _components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/DocumentUploader */ \"(app-pages-browser)/./src/components/DocumentUploader.tsx\");\n/* harmony import */ var _components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/MindMapGenerator */ \"(app-pages-browser)/./src/components/MindMapGenerator.tsx\");\n/* harmony import */ var _components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/FlashcardGenerator */ \"(app-pages-browser)/./src/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/DocumentManager */ \"(app-pages-browser)/./src/components/DocumentManager.tsx\");\n/* harmony import */ var _components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/FlashcardViewer */ \"(app-pages-browser)/./src/components/FlashcardViewer.tsx\");\n/* harmony import */ var _components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/TestGenerator */ \"(app-pages-browser)/./src/components/TestGenerator.tsx\");\n/* harmony import */ var _components_TestViewer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/TestViewer */ \"(app-pages-browser)/./src/components/TestViewer.tsx\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/Dashboard */ \"(app-pages-browser)/./src/components/Dashboard.tsx\");\n/* harmony import */ var _components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/MobileDebugInfo */ \"(app-pages-browser)/./src/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/DiagnosticPanel */ \"(app-pages-browser)/./src/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst TabButton = (param)=>{\n    let { active, onClick, icon, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: \"flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm \".concat(active ? \"text-white \".concat(color, \" shadow-md\") : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 5\n            }, undefined),\n            label,\n            active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiChevronRight, {\n                className: \"ml-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 16\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n};\n_c = TabButton;\nfunction Home() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // No necesitamos verificar autenticación aquí, el middleware ya lo hace\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        isLoading\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        // Recargar la lista de documentos automáticamente\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        // Ocultar el mensaje después de 5 segundos\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        // Recargar la lista de documentos cuando se elimina uno\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const tabs = [\n        {\n            id: 'dashboard',\n            label: 'Dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiRefreshCw, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 50\n            }, this),\n            color: 'bg-gradient-to-r from-blue-600 to-purple-600'\n        },\n        {\n            id: 'preguntas',\n            label: 'Preguntas y Respuestas',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiMessageSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 63\n            }, this),\n            color: 'bg-blue-600'\n        },\n        {\n            id: 'mapas',\n            label: 'Mapas Mentales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiLayers, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 51\n            }, this),\n            color: 'bg-purple-600'\n        },\n        {\n            id: 'flashcards',\n            label: 'Generar Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiFileText, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 60\n            }, this),\n            color: 'bg-orange-500'\n        },\n        {\n            id: 'tests',\n            label: 'Generar Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiList, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 50\n            }, this),\n            color: 'bg-indigo-600'\n        },\n        {\n            id: 'misFlashcards',\n            label: 'Mis Flashcards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiBook, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 59\n            }, this),\n            color: 'bg-emerald-600'\n        },\n        {\n            id: 'misTests',\n            label: 'Mis Tests',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiCheckSquare, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 49\n            }, this),\n            color: 'bg-pink-600'\n        },\n        {\n            id: 'gestionar',\n            label: 'Gestionar Documentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiSettings, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 61\n            }, this),\n            color: 'bg-gray-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Hola, \",\n                                            (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiCheck_FiCheckSquare_FiChevronRight_FiFileText_FiLayers_FiList_FiLogOut_FiMessageSquare_FiRefreshCw_FiSettings_FiUpload_react_icons_fi__WEBPACK_IMPORTED_MODULE_16__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm p-4 sticky top-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2\",\n                                            children: \"Men\\xfa de Estudio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"space-y-1\",\n                                            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabButton, {\n                                                    active: activeTab === tab.id,\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    icon: tab.icon,\n                                                    label: tab.label,\n                                                    color: tab.color\n                                                }, tab.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8 pt-6 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2\",\n                                                    children: \"Documentos Seleccionados\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    ref: documentSelectorRef,\n                                                    onSelectionChange: setDocumentosSeleccionados\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    onNavigateToTab: setActiveTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'preguntas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuestionForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestViewer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"yK1sn0d53DZX6s50SAW2Cl3m8ao=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"TabButton\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW9EO0FBQ1I7QUFDZ0k7QUFDckY7QUFDakM7QUFDUTtBQUNBO0FBQ0k7QUFDTjtBQUNBO0FBQ0o7QUFDTjtBQUNGO0FBQ1k7QUFDQTtBQUVYO0FBWWpELE1BQU02QixZQUFzQztRQUFDLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFO3lCQUNsRiw4REFBQ0M7UUFDQ0osU0FBU0E7UUFDVEssV0FBVywwRkFJVixPQUhDTixTQUNJLGNBQW9CLE9BQU5JLE9BQU0sZ0JBQ3BCOzswQkFHTiw4REFBQ0c7Z0JBQUtELFdBQVU7MEJBQVFKOzs7Ozs7WUFDdkJDO1lBQ0FILHdCQUFVLDhEQUFDckIsa05BQWNBO2dCQUFDMkIsV0FBVTs7Ozs7Ozs7Ozs7OztLQVhuQ1A7QUFlUyxTQUFTUztRQW1GQ0M7O0lBbEZ2QixNQUFNLENBQUNDLHlCQUF5QkMsMkJBQTJCLEdBQUd6QywrQ0FBUUEsQ0FBYyxFQUFFO0lBQ3RGLE1BQU0sQ0FBQzBDLGlCQUFpQkMsbUJBQW1CLEdBQUczQywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUM0QyxXQUFXQyxhQUFhLEdBQUc3QywrQ0FBUUEsQ0FBVTtJQUNwRCxNQUFNLENBQUM4QyxtQkFBbUJDLHFCQUFxQixHQUFHL0MsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDZ0QsdUJBQXVCQyx5QkFBeUIsR0FBR2pELCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sRUFBRWtELFlBQVksRUFBRVgsSUFBSSxFQUFFWSxTQUFTLEVBQUUsR0FBR3ZCLCtEQUFPQTtJQUNqRCxNQUFNd0IsU0FBU2pELDBEQUFTQTtJQUN4QixNQUFNa0Qsc0JBQXNCbkQsNkNBQU1BLENBQXNCO0lBRXhELHdFQUF3RTtJQUN4RUQsZ0RBQVNBOzBCQUFDO1lBQ1JxRCxRQUFRQyxHQUFHLENBQUMsc0NBQXNDSixXQUFXLFNBQVMsQ0FBQyxDQUFDWjtRQUMxRTt5QkFBRztRQUFDQTtRQUFNWTtLQUFVO0lBRXBCLCtEQUErRDtJQUMvRCxJQUFJQSxhQUFhLENBQUNaLE1BQU07UUFDdEIscUJBQ0UsOERBQUNpQjtZQUFJcEIsV0FBVTtzQkFDYiw0RUFBQ29CO2dCQUFJcEIsV0FBVTs7a0NBQ2IsOERBQUNvQjt3QkFBSXBCLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ3FCO3dCQUFFckIsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTFDO0lBRUEsTUFBTXNCLHNCQUFzQjtRQUMxQlgscUJBQXFCO1FBQ3JCRSx5QkFBeUI7UUFFekIsa0RBQWtEO1FBQ2xELElBQUk7Z0JBQ0lJO1lBQU4sUUFBTUEsK0JBQUFBLG9CQUFvQk0sT0FBTyxjQUEzQk4sbURBQUFBLDZCQUE2Qk8sa0JBQWtCO1FBQ3ZELEVBQUUsT0FBT0MsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsaUNBQWlDQTtRQUNqRCxTQUFVO1lBQ1JaLHlCQUF5QjtRQUMzQjtRQUVBLDJDQUEyQztRQUMzQ2EsV0FBVyxJQUFNZixxQkFBcUIsUUFBUTtJQUNoRDtJQUVBLE1BQU1nQix3QkFBd0I7UUFDNUIsd0RBQXdEO1FBQ3hELElBQUk7Z0JBQ0lWO1lBQU4sUUFBTUEsK0JBQUFBLG9CQUFvQk0sT0FBTyxjQUEzQk4sbURBQUFBLDZCQUE2Qk8sa0JBQWtCO1FBQ3ZELEVBQUUsT0FBT0MsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMscURBQXFEQTtRQUNyRTtJQUNGO0lBRUEsTUFBTUcsZUFBZTtRQUNuQixNQUFNZDtJQUNSO0lBRUEsTUFBTWUsT0FBK0U7UUFDbkY7WUFBRUMsSUFBSTtZQUFhakMsT0FBTztZQUFhRCxvQkFBTSw4REFBQ2xCLCtNQUFXQTs7Ozs7WUFBS29CLE9BQU87UUFBK0M7UUFDcEg7WUFBRWdDLElBQUk7WUFBYWpDLE9BQU87WUFBMEJELG9CQUFNLDhEQUFDM0IsbU5BQWVBOzs7OztZQUFLNkIsT0FBTztRQUFjO1FBQ3BHO1lBQUVnQyxJQUFJO1lBQVNqQyxPQUFPO1lBQWtCRCxvQkFBTSw4REFBQzFCLDRNQUFRQTs7Ozs7WUFBSzRCLE9BQU87UUFBZ0I7UUFDbkY7WUFBRWdDLElBQUk7WUFBY2pDLE9BQU87WUFBc0JELG9CQUFNLDhEQUFDekIsOE1BQVVBOzs7OztZQUFLMkIsT0FBTztRQUFnQjtRQUM5RjtZQUFFZ0MsSUFBSTtZQUFTakMsT0FBTztZQUFpQkQsb0JBQU0sOERBQUNyQiwwTUFBTUE7Ozs7O1lBQUt1QixPQUFPO1FBQWdCO1FBQ2hGO1lBQUVnQyxJQUFJO1lBQWlCakMsT0FBTztZQUFrQkQsb0JBQU0sOERBQUM1QiwwTUFBTUE7Ozs7O1lBQUs4QixPQUFPO1FBQWlCO1FBQzFGO1lBQUVnQyxJQUFJO1lBQVlqQyxPQUFPO1lBQWFELG9CQUFNLDhEQUFDcEIsaU5BQWFBOzs7OztZQUFLc0IsT0FBTztRQUFjO1FBQ3BGO1lBQUVnQyxJQUFJO1lBQWFqQyxPQUFPO1lBQXdCRCxvQkFBTSw4REFBQ2pCLDhNQUFVQTs7Ozs7WUFBS21CLE9BQU87UUFBYztLQUM5RjtJQUVELHFCQUNFLDhEQUFDc0I7UUFBSXBCLFdBQVU7OzBCQUViLDhEQUFDK0I7Z0JBQU8vQixXQUFVOzBCQUNoQiw0RUFBQ29CO29CQUFJcEIsV0FBVTs4QkFDYiw0RUFBQ29CO3dCQUFJcEIsV0FBVTs7MENBQ2IsOERBQUNvQjs7a0RBQ0MsOERBQUNZO3dDQUFHaEMsV0FBVTtrREFBZ0c7Ozs7OztrREFHOUcsOERBQUNxQjt3Q0FBRXJCLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBRXZDLDhEQUFDb0I7Z0NBQUlwQixXQUFVOztvQ0FDWkcsc0JBQ0MsOERBQUNpQjt3Q0FBSXBCLFdBQVU7OzRDQUF3Qjs2Q0FDOUJHLGNBQUFBLEtBQUs4QixLQUFLLGNBQVY5QixrQ0FBQUEsWUFBWStCLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTs7Ozs7OztrREFHcEMsOERBQUNuQzt3Q0FDQ0osU0FBUyxJQUFNWSxtQkFBbUIsQ0FBQ0Q7d0NBQ25DTixXQUFVOzswREFFViw4REFBQzVCLDRNQUFRQTtnREFBQzRCLFdBQVU7Ozs7Ozs0Q0FDbkJNLGtCQUFrQix1QkFBdUI7Ozs7Ozs7a0RBRTVDLDhEQUFDUDt3Q0FDQ0osU0FBU2lDO3dDQUNUNUIsV0FBVTs7MERBRVYsOERBQUN2Qiw0TUFBUUE7Z0RBQUN1QixXQUFVOzs7Ozs7NENBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVF2Qyw4REFBQ21DO2dCQUFLbkMsV0FBVTs7b0JBRWJNLGlDQUNDLDhEQUFDYzt3QkFBSXBCLFdBQVU7a0NBQ2IsNEVBQUNsQixvRUFBZ0JBOzRCQUFDc0QsV0FBV2Q7Ozs7Ozs7Ozs7O29CQUloQ1osbUNBQ0MsOERBQUNVO3dCQUFJcEIsV0FBVTtrQ0FDYiw0RUFBQ29COzRCQUFJcEIsV0FBVTs7OENBQ2IsOERBQUMxQiwyTUFBT0E7b0NBQUMwQixXQUFVOzs7Ozs7OENBQ25CLDhEQUFDb0I7O3NEQUNDLDhEQUFDQzs0Q0FBRXJCLFdBQVU7c0RBQWM7Ozs7OztzREFDM0IsOERBQUNxQjs0Q0FBRXJCLFdBQVU7c0RBQ1ZZLHNDQUNDLDhEQUFDWDtnREFBS0QsV0FBVTs7a0VBQ2QsOERBQUN0QiwrTUFBV0E7d0RBQUNzQixXQUFVOzs7Ozs7b0RBQXNCOzs7Ozs7dURBSS9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTWiw4REFBQ29CO3dCQUFJcEIsV0FBVTs7MENBRWIsOERBQUNvQjtnQ0FBSXBCLFdBQVU7MENBQ2IsNEVBQUNvQjtvQ0FBSXBCLFdBQVU7O3NEQUNiLDhEQUFDcUM7NENBQUdyQyxXQUFVO3NEQUF5RTs7Ozs7O3NEQUd2Riw4REFBQ3NDOzRDQUFJdEMsV0FBVTtzREFDWjZCLEtBQUtVLEdBQUcsQ0FBQyxDQUFDQyxvQkFDVCw4REFBQy9DO29EQUVDQyxRQUFRYyxjQUFjZ0MsSUFBSVYsRUFBRTtvREFDNUJuQyxTQUFTLElBQU1jLGFBQWErQixJQUFJVixFQUFFO29EQUNsQ2xDLE1BQU00QyxJQUFJNUMsSUFBSTtvREFDZEMsT0FBTzJDLElBQUkzQyxLQUFLO29EQUNoQkMsT0FBTzBDLElBQUkxQyxLQUFLO21EQUxYMEMsSUFBSVYsRUFBRTs7Ozs7Ozs7OztzREFVakIsOERBQUNWOzRDQUFJcEIsV0FBVTs7OERBQ2IsOERBQUN5QztvREFBR3pDLFdBQVU7OERBQXlFOzs7Ozs7OERBR3ZGLDhEQUFDcEIsb0VBQWdCQTtvREFDZjhELEtBQUt6QjtvREFDTDBCLG1CQUFtQnRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPM0IsOERBQUNlO2dDQUFJcEIsV0FBVTswQ0FDWlEsY0FBYyw0QkFDYiw4REFBQ25CLDhEQUFTQTtvQ0FBQ3VELGlCQUFpQm5DOzs7Ozt5REFFNUIsOERBQUNXO29DQUFJcEIsV0FBVTs4Q0FDYiw0RUFBQ29CO3dDQUFJcEIsV0FBVTs7NENBQ1pRLGNBQWMsNkJBQ2IsOERBQUMzQixnRUFBWUE7Z0RBQUN1Qix5QkFBeUJBOzs7Ozs7NENBR3hDSSxjQUFjLHlCQUNiLDhEQUFDekIsb0VBQWdCQTtnREFBQ3FCLHlCQUF5QkE7Ozs7Ozs0Q0FHNUNJLGNBQWMsOEJBQ2IsOERBQUN4QixzRUFBa0JBO2dEQUFDb0IseUJBQXlCQTs7Ozs7OzRDQUc5Q0ksY0FBYyx5QkFDYiw4REFBQ3JCLGtFQUFhQTtnREFBQ2lCLHlCQUF5QkE7Ozs7Ozs0Q0FHekNJLGNBQWMsNEJBQWMsOERBQUNwQiwrREFBVUE7Ozs7OzRDQUV2Q29CLGNBQWMsaUNBQW1CLDhEQUFDdEIsbUVBQWVBOzs7Ozs0Q0FFakRzQixjQUFjLDZCQUNiLDhEQUFDdkIsbUVBQWVBO2dEQUFDNEQsbUJBQW1CbEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVWxELDhEQUFDbUI7Z0JBQU85QyxXQUFVOzBCQUNoQiw0RUFBQ29CO29CQUFJcEIsV0FBVTs4QkFDYiw0RUFBQ29CO3dCQUFJcEIsV0FBVTs7MENBQ2IsOERBQUNvQjtnQ0FBSXBCLFdBQVU7MENBQ2IsNEVBQUNDO29DQUFLRCxXQUFVOzt3Q0FBd0I7d0NBQzlCLElBQUkrQyxPQUFPQyxXQUFXO3dDQUFHOzs7Ozs7Ozs7Ozs7MENBR3JDLDhEQUFDNUI7Z0NBQUlwQixXQUFVOzBDQUNiLDRFQUFDc0M7b0NBQUl0QyxXQUFVOztzREFDYiw4REFBQ2lEOzRDQUFFQyxNQUFLOzRDQUFJbEQsV0FBVTtzREFBNEM7Ozs7OztzREFHbEUsOERBQUNpRDs0Q0FBRUMsTUFBSzs0Q0FBSWxELFdBQVU7c0RBQTRDOzs7Ozs7c0RBR2xFLDhEQUFDaUQ7NENBQUVDLE1BQUs7NENBQUlsRCxXQUFVO3NEQUE0Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVU1RSw4REFBQ1Ysb0VBQWVBOzs7OzswQkFHaEIsOERBQUNDLG9FQUFlQTs7Ozs7Ozs7Ozs7QUFHdEI7R0E3T3dCVzs7UUFNb0JWLDJEQUFPQTtRQUNsQ3pCLHNEQUFTQTs7O01BUEZtQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcYXBwXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBGaUJvb2ssIEZpTWVzc2FnZVNxdWFyZSwgRmlMYXllcnMsIEZpRmlsZVRleHQsIEZpVXBsb2FkLCBGaUNoZXZyb25SaWdodCwgRmlDaGVjaywgRmlMaXN0LCBGaUNoZWNrU3F1YXJlLCBGaUxvZ091dCwgRmlSZWZyZXNoQ3csIEZpU2V0dGluZ3MgfSBmcm9tICdyZWFjdC1pY29ucy9maSc7XG5pbXBvcnQgRG9jdW1lbnRTZWxlY3RvciwgeyBEb2N1bWVudFNlbGVjdG9yUmVmIH0gZnJvbSAnLi4vY29tcG9uZW50cy9Eb2N1bWVudFNlbGVjdG9yJztcbmltcG9ydCBRdWVzdGlvbkZvcm0gZnJvbSAnLi4vY29tcG9uZW50cy9RdWVzdGlvbkZvcm0nO1xuaW1wb3J0IERvY3VtZW50VXBsb2FkZXIgZnJvbSAnLi4vY29tcG9uZW50cy9Eb2N1bWVudFVwbG9hZGVyJztcbmltcG9ydCBNaW5kTWFwR2VuZXJhdG9yIGZyb20gJy4uL2NvbXBvbmVudHMvTWluZE1hcEdlbmVyYXRvcic7XG5pbXBvcnQgRmxhc2hjYXJkR2VuZXJhdG9yIGZyb20gJy4uL2NvbXBvbmVudHMvRmxhc2hjYXJkR2VuZXJhdG9yJztcbmltcG9ydCBEb2N1bWVudE1hbmFnZXIgZnJvbSAnLi4vY29tcG9uZW50cy9Eb2N1bWVudE1hbmFnZXInO1xuaW1wb3J0IEZsYXNoY2FyZFZpZXdlciBmcm9tICcuLi9jb21wb25lbnRzL0ZsYXNoY2FyZFZpZXdlcic7XG5pbXBvcnQgVGVzdEdlbmVyYXRvciBmcm9tICcuLi9jb21wb25lbnRzL1Rlc3RHZW5lcmF0b3InO1xuaW1wb3J0IFRlc3RWaWV3ZXIgZnJvbSAnLi4vY29tcG9uZW50cy9UZXN0Vmlld2VyJztcbmltcG9ydCBEYXNoYm9hcmQgZnJvbSAnLi4vY29tcG9uZW50cy9EYXNoYm9hcmQnO1xuaW1wb3J0IE1vYmlsZURlYnVnSW5mbyBmcm9tICcuLi9jb21wb25lbnRzL01vYmlsZURlYnVnSW5mbyc7XG5pbXBvcnQgRGlhZ25vc3RpY1BhbmVsIGZyb20gJy4uL2NvbXBvbmVudHMvRGlhZ25vc3RpY1BhbmVsJztcbmltcG9ydCB7IERvY3VtZW50byB9IGZyb20gJy4uL2xpYi9zdXBhYmFzZSc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5cbnR5cGUgVGFiVHlwZSA9ICdkYXNoYm9hcmQnIHwgJ3ByZWd1bnRhcycgfCAnbWFwYXMnIHwgJ2ZsYXNoY2FyZHMnIHwgJ21pc0ZsYXNoY2FyZHMnIHwgJ3Rlc3RzJyB8ICdtaXNUZXN0cycgfCAnZ2VzdGlvbmFyJztcblxuaW50ZXJmYWNlIFRhYkJ1dHRvblByb3BzIHtcbiAgYWN0aXZlOiBib29sZWFuO1xuICBvbkNsaWNrOiAoKSA9PiB2b2lkO1xuICBpY29uOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGxhYmVsOiBzdHJpbmc7XG4gIGNvbG9yOiBzdHJpbmc7XG59XG5cbmNvbnN0IFRhYkJ1dHRvbjogUmVhY3QuRkM8VGFiQnV0dG9uUHJvcHM+ID0gKHsgYWN0aXZlLCBvbkNsaWNrLCBpY29uLCBsYWJlbCwgY29sb3IgfSkgPT4gKFxuICA8YnV0dG9uXG4gICAgb25DbGljaz17b25DbGlja31cbiAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTMgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9udC1tZWRpdW0gdGV4dC1zbSAke1xuICAgICAgYWN0aXZlXG4gICAgICAgID8gYHRleHQtd2hpdGUgJHtjb2xvcn0gc2hhZG93LW1kYFxuICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktMTAwIGhvdmVyOnRleHQtZ3JheS04MDAnXG4gICAgfWB9XG4gID5cbiAgICA8c3BhbiBjbGFzc05hbWU9XCJtci0yXCI+e2ljb259PC9zcGFuPlxuICAgIHtsYWJlbH1cbiAgICB7YWN0aXZlICYmIDxGaUNoZXZyb25SaWdodCBjbGFzc05hbWU9XCJtbC0yXCIgLz59XG4gIDwvYnV0dG9uPlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW2RvY3VtZW50b3NTZWxlY2Npb25hZG9zLCBzZXREb2N1bWVudG9zU2VsZWNjaW9uYWRvc10gPSB1c2VTdGF0ZTxEb2N1bWVudG9bXT4oW10pO1xuICBjb25zdCBbbW9zdHJhclVwbG9hZGVyLCBzZXRNb3N0cmFyVXBsb2FkZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8VGFiVHlwZT4oJ2Rhc2hib2FyZCcpO1xuICBjb25zdCBbc2hvd1VwbG9hZFN1Y2Nlc3MsIHNldFNob3dVcGxvYWRTdWNjZXNzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUmVmcmVzaGluZ0RvY3VtZW50cywgc2V0SXNSZWZyZXNoaW5nRG9jdW1lbnRzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgeyBjZXJyYXJTZXNpb24sIHVzZXIsIGlzTG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgZG9jdW1lbnRTZWxlY3RvclJlZiA9IHVzZVJlZjxEb2N1bWVudFNlbGVjdG9yUmVmPihudWxsKTtcblxuICAvLyBObyBuZWNlc2l0YW1vcyB2ZXJpZmljYXIgYXV0ZW50aWNhY2nDs24gYXF1w60sIGVsIG1pZGRsZXdhcmUgeWEgbG8gaGFjZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdbSG9tZVBhZ2VdIEF1dGggc3RhdGUgLSBpc0xvYWRpbmc6JywgaXNMb2FkaW5nLCAnVXNlcjonLCAhIXVzZXIpO1xuICB9LCBbdXNlciwgaXNMb2FkaW5nXSk7XG5cbiAgLy8gU2kgZXN0w6EgY2FyZ2FuZG8gbyBubyBoYXkgdXN1YXJpbywgbW9zdHJhciBwYW50YWxsYSBkZSBjYXJnYVxuICBpZiAoaXNMb2FkaW5nIHx8ICF1c2VyKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNTAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtZ3JheS02MDBcIj5DYXJnYW5kby4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgY29uc3QgaGFuZGxlVXBsb2FkU3VjY2VzcyA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRTaG93VXBsb2FkU3VjY2Vzcyh0cnVlKTtcbiAgICBzZXRJc1JlZnJlc2hpbmdEb2N1bWVudHModHJ1ZSk7XG5cbiAgICAvLyBSZWNhcmdhciBsYSBsaXN0YSBkZSBkb2N1bWVudG9zIGF1dG9tw6F0aWNhbWVudGVcbiAgICB0cnkge1xuICAgICAgYXdhaXQgZG9jdW1lbnRTZWxlY3RvclJlZi5jdXJyZW50Py5yZWNhcmdhckRvY3VtZW50b3MoKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgcmVjYXJnYXIgZG9jdW1lbnRvczonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzUmVmcmVzaGluZ0RvY3VtZW50cyhmYWxzZSk7XG4gICAgfVxuXG4gICAgLy8gT2N1bHRhciBlbCBtZW5zYWplIGRlc3B1w6lzIGRlIDUgc2VndW5kb3NcbiAgICBzZXRUaW1lb3V0KCgpID0+IHNldFNob3dVcGxvYWRTdWNjZXNzKGZhbHNlKSwgNTAwMCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRG9jdW1lbnREZWxldGVkID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIFJlY2FyZ2FyIGxhIGxpc3RhIGRlIGRvY3VtZW50b3MgY3VhbmRvIHNlIGVsaW1pbmEgdW5vXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGRvY3VtZW50U2VsZWN0b3JSZWYuY3VycmVudD8ucmVjYXJnYXJEb2N1bWVudG9zKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIHJlY2FyZ2FyIGRvY3VtZW50b3MgZGVzcHXDqXMgZGUgZWxpbWluYXI6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgY2VycmFyU2VzaW9uKCk7XG4gIH07XG5cbiAgY29uc3QgdGFiczogeyBpZDogVGFiVHlwZTsgbGFiZWw6IHN0cmluZzsgaWNvbjogUmVhY3QuUmVhY3ROb2RlOyBjb2xvcjogc3RyaW5nIH1bXSA9IFtcbiAgICB7IGlkOiAnZGFzaGJvYXJkJywgbGFiZWw6ICdEYXNoYm9hcmQnLCBpY29uOiA8RmlSZWZyZXNoQ3cgLz4sIGNvbG9yOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAnIH0sXG4gICAgeyBpZDogJ3ByZWd1bnRhcycsIGxhYmVsOiAnUHJlZ3VudGFzIHkgUmVzcHVlc3RhcycsIGljb246IDxGaU1lc3NhZ2VTcXVhcmUgLz4sIGNvbG9yOiAnYmctYmx1ZS02MDAnIH0sXG4gICAgeyBpZDogJ21hcGFzJywgbGFiZWw6ICdNYXBhcyBNZW50YWxlcycsIGljb246IDxGaUxheWVycyAvPiwgY29sb3I6ICdiZy1wdXJwbGUtNjAwJyB9LFxuICAgIHsgaWQ6ICdmbGFzaGNhcmRzJywgbGFiZWw6ICdHZW5lcmFyIEZsYXNoY2FyZHMnLCBpY29uOiA8RmlGaWxlVGV4dCAvPiwgY29sb3I6ICdiZy1vcmFuZ2UtNTAwJyB9LFxuICAgIHsgaWQ6ICd0ZXN0cycsIGxhYmVsOiAnR2VuZXJhciBUZXN0cycsIGljb246IDxGaUxpc3QgLz4sIGNvbG9yOiAnYmctaW5kaWdvLTYwMCcgfSxcbiAgICB7IGlkOiAnbWlzRmxhc2hjYXJkcycsIGxhYmVsOiAnTWlzIEZsYXNoY2FyZHMnLCBpY29uOiA8RmlCb29rIC8+LCBjb2xvcjogJ2JnLWVtZXJhbGQtNjAwJyB9LFxuICAgIHsgaWQ6ICdtaXNUZXN0cycsIGxhYmVsOiAnTWlzIFRlc3RzJywgaWNvbjogPEZpQ2hlY2tTcXVhcmUgLz4sIGNvbG9yOiAnYmctcGluay02MDAnIH0sXG4gICAgeyBpZDogJ2dlc3Rpb25hcicsIGxhYmVsOiAnR2VzdGlvbmFyIERvY3VtZW50b3MnLCBpY29uOiA8RmlTZXR0aW5ncyAvPiwgY29sb3I6ICdiZy1ncmF5LTYwMCcgfSxcbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgIE9wb3NpQUlcbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+VHUgYXNpc3RlbnRlIGludGVsaWdlbnRlIHBhcmEgb3Bvc2ljaW9uZXM8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIHt1c2VyICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgSG9sYSwge3VzZXIuZW1haWw/LnNwbGl0KCdAJylbMF19XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb3N0cmFyVXBsb2FkZXIoIW1vc3RyYXJVcGxvYWRlcil9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC13aGl0ZSBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tYmx1ZS03MDAgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1ibHVlLTgwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEZpVXBsb2FkIGNsYXNzTmFtZT1cIm1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIHttb3N0cmFyVXBsb2FkZXIgPyAnT2N1bHRhciBmb3JtdWxhcmlvJyA6ICdOdWV2byBkb2N1bWVudG8nfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUxvZ291dH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEZpTG9nT3V0IGNsYXNzTmFtZT1cIm1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIENlcnJhciBzZXNpw7NuXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cInB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgey8qIFVwbG9hZGVyICovfVxuICAgICAgICB7bW9zdHJhclVwbG9hZGVyICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0XCI+XG4gICAgICAgICAgICA8RG9jdW1lbnRVcGxvYWRlciBvblN1Y2Nlc3M9e2hhbmRsZVVwbG9hZFN1Y2Nlc3N9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge3Nob3dVcGxvYWRTdWNjZXNzICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJnLWdyZWVuLTUwIHRleHQtZ3JlZW4tODAwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEZpQ2hlY2sgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDAgbXItMiBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPsKhRG9jdW1lbnRvIHN1YmlkbyBleGl0b3NhbWVudGUhPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi03MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAge2lzUmVmcmVzaGluZ0RvY3VtZW50cyA/IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8RmlSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEFjdHVhbGl6YW5kbyBsaXN0YSBkZSBkb2N1bWVudG9zLi4uXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIFwiRWwgZG9jdW1lbnRvIHlhIGVzdMOhIGRpc3BvbmlibGUgZW4gbG9zIGRlc3BsZWdhYmxlcyBkZSBzZWxlY2Npw7NuLlwiXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogTGF5b3V0IGV4cGFuZGlkbyBwYXJhIHRvZGFzIGxhcyBwZXN0YcOxYXMgLSBhbmNobyBjb21wbGV0byAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC02IG1iLThcIj5cbiAgICAgICAgICB7LyogU2lkZWJhciBjb21wYWN0byAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctODAgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBwLTQgc3RpY2t5IHRvcC02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXIgbWItNCBweC0yXCI+XG4gICAgICAgICAgICAgICAgTWVuw7ogZGUgRXN0dWRpb1xuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgIHt0YWJzLm1hcCgodGFiKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8VGFiQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17dGFiLmlkfVxuICAgICAgICAgICAgICAgICAgICBhY3RpdmU9e2FjdGl2ZVRhYiA9PT0gdGFiLmlkfVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIodGFiLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgaWNvbj17dGFiLmljb259XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPXt0YWIubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgIGNvbG9yPXt0YWIuY29sb3J9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L25hdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTggcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyIG1iLTMgcHgtMlwiPlxuICAgICAgICAgICAgICAgICAgRG9jdW1lbnRvcyBTZWxlY2Npb25hZG9zXG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8RG9jdW1lbnRTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgcmVmPXtkb2N1bWVudFNlbGVjdG9yUmVmfVxuICAgICAgICAgICAgICAgICAgb25TZWxlY3Rpb25DaGFuZ2U9e3NldERvY3VtZW50b3NTZWxlY2Npb25hZG9zfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyogw4FyZWEgZGUgY29udGVuaWRvIGV4cGFuZGlkYSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2Rhc2hib2FyZCcgPyAoXG4gICAgICAgICAgICAgIDxEYXNoYm9hcmQgb25OYXZpZ2F0ZVRvVGFiPXtzZXRBY3RpdmVUYWJ9IC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXNtIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAncHJlZ3VudGFzJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxRdWVzdGlvbkZvcm0gZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3M9e2RvY3VtZW50b3NTZWxlY2Npb25hZG9zfSAvPlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ21hcGFzJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxNaW5kTWFwR2VuZXJhdG9yIGRvY3VtZW50b3NTZWxlY2Npb25hZG9zPXtkb2N1bWVudG9zU2VsZWNjaW9uYWRvc30gLz5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdmbGFzaGNhcmRzJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxGbGFzaGNhcmRHZW5lcmF0b3IgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3M9e2RvY3VtZW50b3NTZWxlY2Npb25hZG9zfSAvPlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3Rlc3RzJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxUZXN0R2VuZXJhdG9yIGRvY3VtZW50b3NTZWxlY2Npb25hZG9zPXtkb2N1bWVudG9zU2VsZWNjaW9uYWRvc30gLz5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdtaXNUZXN0cycgJiYgPFRlc3RWaWV3ZXIgLz59XG5cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdtaXNGbGFzaGNhcmRzJyAmJiA8Rmxhc2hjYXJkVmlld2VyIC8+fVxuXG4gICAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnZ2VzdGlvbmFyJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxEb2N1bWVudE1hbmFnZXIgb25Eb2N1bWVudERlbGV0ZWQ9e2hhbmRsZURvY3VtZW50RGVsZXRlZH0gLz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBtdC0xMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAmY29weTsge25ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKX0gT3Bvc2lBSSAtIEFzaXN0ZW50ZSBwYXJhIE9wb3NpY2lvbmVzXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IG1kOm10LTBcIj5cbiAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNlwiPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIFTDqXJtaW5vc1xuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICBQcml2YWNpZGFkXG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIENvbnRhY3RvXG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuXG4gICAgICB7LyogQ29tcG9uZW50ZSBkZSBkZWJ1ZyBwYXJhIGRpc3Bvc2l0aXZvcyBtw7N2aWxlcyAqL31cbiAgICAgIDxNb2JpbGVEZWJ1Z0luZm8gLz5cblxuICAgICAgey8qIFBhbmVsIGRlIGRpYWduw7NzdGljbyAqL31cbiAgICAgIDxEaWFnbm9zdGljUGFuZWwgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVJvdXRlciIsIkZpQm9vayIsIkZpTWVzc2FnZVNxdWFyZSIsIkZpTGF5ZXJzIiwiRmlGaWxlVGV4dCIsIkZpVXBsb2FkIiwiRmlDaGV2cm9uUmlnaHQiLCJGaUNoZWNrIiwiRmlMaXN0IiwiRmlDaGVja1NxdWFyZSIsIkZpTG9nT3V0IiwiRmlSZWZyZXNoQ3ciLCJGaVNldHRpbmdzIiwiRG9jdW1lbnRTZWxlY3RvciIsIlF1ZXN0aW9uRm9ybSIsIkRvY3VtZW50VXBsb2FkZXIiLCJNaW5kTWFwR2VuZXJhdG9yIiwiRmxhc2hjYXJkR2VuZXJhdG9yIiwiRG9jdW1lbnRNYW5hZ2VyIiwiRmxhc2hjYXJkVmlld2VyIiwiVGVzdEdlbmVyYXRvciIsIlRlc3RWaWV3ZXIiLCJEYXNoYm9hcmQiLCJNb2JpbGVEZWJ1Z0luZm8iLCJEaWFnbm9zdGljUGFuZWwiLCJ1c2VBdXRoIiwiVGFiQnV0dG9uIiwiYWN0aXZlIiwib25DbGljayIsImljb24iLCJsYWJlbCIsImNvbG9yIiwiYnV0dG9uIiwiY2xhc3NOYW1lIiwic3BhbiIsIkhvbWUiLCJ1c2VyIiwiZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MiLCJzZXREb2N1bWVudG9zU2VsZWNjaW9uYWRvcyIsIm1vc3RyYXJVcGxvYWRlciIsInNldE1vc3RyYXJVcGxvYWRlciIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInNob3dVcGxvYWRTdWNjZXNzIiwic2V0U2hvd1VwbG9hZFN1Y2Nlc3MiLCJpc1JlZnJlc2hpbmdEb2N1bWVudHMiLCJzZXRJc1JlZnJlc2hpbmdEb2N1bWVudHMiLCJjZXJyYXJTZXNpb24iLCJpc0xvYWRpbmciLCJyb3V0ZXIiLCJkb2N1bWVudFNlbGVjdG9yUmVmIiwiY29uc29sZSIsImxvZyIsImRpdiIsInAiLCJoYW5kbGVVcGxvYWRTdWNjZXNzIiwiY3VycmVudCIsInJlY2FyZ2FyRG9jdW1lbnRvcyIsImVycm9yIiwic2V0VGltZW91dCIsImhhbmRsZURvY3VtZW50RGVsZXRlZCIsImhhbmRsZUxvZ291dCIsInRhYnMiLCJpZCIsImhlYWRlciIsImgxIiwiZW1haWwiLCJzcGxpdCIsIm1haW4iLCJvblN1Y2Nlc3MiLCJoMiIsIm5hdiIsIm1hcCIsInRhYiIsImgzIiwicmVmIiwib25TZWxlY3Rpb25DaGFuZ2UiLCJvbk5hdmlnYXRlVG9UYWIiLCJvbkRvY3VtZW50RGVsZXRlZCIsImZvb3RlciIsIkRhdGUiLCJnZXRGdWxsWWVhciIsImEiLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FlashcardViewer.tsx":
/*!********************************************!*\
  !*** ./src/components/FlashcardViewer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _flashcards_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./flashcards/FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _flashcards_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./flashcards/FlashcardCollectionView */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionView.tsx\");\n/* harmony import */ var _flashcards_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./flashcards/FlashcardStudyOptions */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx\");\n/* harmony import */ var _flashcards_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./flashcards/FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _StudyStatistics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./StudyStatistics */ \"(app-pages-browser)/./src/components/StudyStatistics.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction FlashcardViewer() {\n    _s();\n    // Estados principales\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingColecciones, setIsLoadingColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales y modos\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticas, setMostrarEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoadingColecciones(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoadingColecciones(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const iniciarModoEstudio = async function() {\n        let tipoEstudio = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'programadas';\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                let flashcardsParaEstudiar = [];\n                // Obtener flashcards según el tipo de estudio seleccionado\n                switch(tipoEstudio){\n                    case 'programadas':\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                        break;\n                    case 'dificiles':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsMasDificiles)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'aleatorias':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsAleatorias)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'no-recientes':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsNoRecientes)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'nuevas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'nuevo', 20);\n                        break;\n                    case 'aprendiendo':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendiendo', 20);\n                        break;\n                    case 'repasando':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'repasando', 20);\n                        break;\n                    case 'aprendidas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendido', 20);\n                        break;\n                    default:\n                        const defaultData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = defaultData.filter((flashcard)=>flashcard.debeEstudiar);\n                }\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Si no hay flashcards para el tipo seleccionado, mostrar mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (tipoEstudio === 'programadas') {\n                        alert('No hay flashcards programadas para estudiar hoy. Puedes usar \"Opciones de estudio\" para elegir otro tipo de repaso.');\n                        return;\n                    } else {\n                        alert(\"No hay flashcards disponibles para el tipo de estudio seleccionado.\");\n                        return;\n                    }\n                }\n                // Usar las flashcards obtenidas\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarOpcionesEstudio(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo estudio:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const salirModoEstudio = async ()=>{\n        setModoEstudio(false);\n        // Recargar las flashcards y estadísticas\n        if (coleccionSeleccionada) {\n            try {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const ordenadas = [\n                    ...data\n                ].sort((a, b)=>{\n                    if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                    if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                    return 0;\n                });\n                setFlashcards(ordenadas);\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n            } catch (error) {\n                console.error('Error al recargar datos:', error);\n            }\n        }\n    };\n    const handleRespuesta = async (dificultad)=>{\n        if (!flashcards[activeIndex]) return;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcards[activeIndex].id, dificultad);\n            if (!exito) {\n                throw new Error('Error al registrar la respuesta');\n            }\n            // Si es la última tarjeta, terminar la sesión\n            if (activeIndex >= flashcards.length - 1) {\n                alert('¡Has completado la sesión de estudio!');\n                await salirModoEstudio();\n            } else {\n                // Avanzar a la siguiente tarjeta\n                setActiveIndex(activeIndex + 1);\n            }\n        } catch (error) {\n            console.error('Error al actualizar progreso:', error);\n            setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');\n        } finally{\n            setRespondiendo(false);\n        }\n    };\n    const handleNavigate = (direction)=>{\n        if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n        } else if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n        }\n    };\n    const handleEliminarColeccion = async (coleccionId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingId(coleccionId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (exito) {\n                // Recargar las colecciones\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                setColecciones(data);\n                // Si la colección eliminada era la seleccionada, deseleccionarla\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                setError('No se pudo eliminar la colección');\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            setError('Error al eliminar la colección');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: salirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionSeleccionada ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoadingColecciones,\n                        deletingId: deletingId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setColeccionSeleccionada(null),\n                                className: \"mb-4 text-blue-600 hover:text-blue-800 flex items-center\",\n                                children: \"← Volver a mis colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                coleccion: coleccionSeleccionada,\n                                flashcards: flashcards,\n                                estadisticas: estadisticas,\n                                isLoading: isLoading,\n                                onStartStudy: ()=>iniciarModoEstudio('programadas'),\n                                onShowStudyOptions: ()=>setMostrarOpcionesEstudio(true),\n                                onShowStatistics: ()=>setMostrarEstadisticas(true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: mostrarOpcionesEstudio,\n                onClose: ()=>setMostrarOpcionesEstudio(false),\n                onSelectStudyType: iniciarModoEstudio,\n                estadisticas: estadisticas,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this),\n            mostrarEstadisticas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudyStatistics__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                onClose: ()=>setMostrarEstadisticas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardViewer, \"ydFcPtLOP5ghLJe9pJKQEAkrVf4=\");\n_c = FlashcardViewer;\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FlashcardViewer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/StudyStatistics.tsx":
/*!********************************************!*\
  !*** ./src/components/StudyStatistics.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudyStatistics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nvar _s = $RefreshSig$();\n\n\nfunction StudyStatistics(param) {\n    let { coleccionId, onClose } = param;\n    _s();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudyStatistics.useEffect\": ()=>{\n            const cargarEstadisticas = {\n                \"StudyStatistics.useEffect.cargarEstadisticas\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasDetalladas)(coleccionId);\n                        setEstadisticas(data);\n                    } catch (error) {\n                        console.error('Error al cargar estadísticas:', error);\n                        setError('No se pudieron cargar las estadísticas detalladas');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"StudyStatistics.useEffect.cargarEstadisticas\"];\n            cargarEstadisticas();\n        }\n    }[\"StudyStatistics.useEffect\"], [\n        coleccionId\n    ]);\n    // Formatear la fecha para mostrarla en la lista\n    const formatearFecha = (fechaStr)=>{\n        const fecha = new Date(fechaStr);\n        return fecha.toLocaleDateString('es-ES', {\n            day: '2-digit',\n            month: '2-digit',\n            year: 'numeric'\n        });\n    };\n    // Calcular el porcentaje para las barras de progreso\n    const calcularPorcentaje = (valor, total)=>{\n        if (total === 0) return 0;\n        return Math.round(valor / total * 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold\",\n                            children: \"Estad\\xedsticas detalladas de estudio\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('general'),\n                                className: \"px-4 py-2 font-medium \".concat(activeTab === 'general' ? 'border-b-2 border-orange-500 text-orange-600' : 'text-gray-600 hover:text-gray-800'),\n                                children: \"General\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('progreso'),\n                                className: \"px-4 py-2 font-medium \".concat(activeTab === 'progreso' ? 'border-b-2 border-orange-500 text-orange-600' : 'text-gray-600 hover:text-gray-800'),\n                                children: \"Progreso\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab('dificiles'),\n                                className: \"px-4 py-2 font-medium \".concat(activeTab === 'dificiles' ? 'border-b-2 border-orange-500 text-orange-600' : 'text-gray-600 hover:text-gray-800'),\n                                children: \"Tarjetas dif\\xedciles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 overflow-y-auto flex-grow\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center h-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 text-center py-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this) : !estadisticas ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-500 text-center py-4\",\n                        children: \"No hay datos estad\\xedsticos disponibles\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            activeTab === 'general' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2\",\n                                                        children: \"Sesiones de estudio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-orange-600\",\n                                                        children: estadisticas.totalSesiones\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2\",\n                                                        children: \"Total de revisiones\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-orange-600\",\n                                                        children: estadisticas.totalRevisiones\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold mb-2\",\n                                                        children: \"Promedio por sesi\\xf3n\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-orange-600\",\n                                                        children: estadisticas.totalSesiones > 0 ? Math.round(estadisticas.totalRevisiones / estadisticas.totalSesiones) : 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-3\",\n                                                children: \"Distribuci\\xf3n de respuestas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Dif\\xedcil\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: [\n                                                                            estadisticas.distribucionDificultad.dificil,\n                                                                            \" (\",\n                                                                            calcularPorcentaje(estadisticas.distribucionDificultad.dificil, estadisticas.totalRevisiones),\n                                                                            \"%)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 136,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-red-500 h-2.5 rounded-full\",\n                                                                    style: {\n                                                                        width: \"\".concat(calcularPorcentaje(estadisticas.distribucionDificultad.dificil, estadisticas.totalRevisiones), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Normal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: [\n                                                                            estadisticas.distribucionDificultad.normal,\n                                                                            \" (\",\n                                                                            calcularPorcentaje(estadisticas.distribucionDificultad.normal, estadisticas.totalRevisiones),\n                                                                            \"%)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-yellow-500 h-2.5 rounded-full\",\n                                                                    style: {\n                                                                        width: \"\".concat(calcularPorcentaje(estadisticas.distribucionDificultad.normal, estadisticas.totalRevisiones), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"F\\xe1cil\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: [\n                                                                            estadisticas.distribucionDificultad.facil,\n                                                                            \" (\",\n                                                                            calcularPorcentaje(estadisticas.distribucionDificultad.facil, estadisticas.totalRevisiones),\n                                                                            \"%)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-green-500 h-2.5 rounded-full\",\n                                                                    style: {\n                                                                        width: \"\".concat(calcularPorcentaje(estadisticas.distribucionDificultad.facil, estadisticas.totalRevisiones), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 17\n                            }, this),\n                            activeTab === 'progreso' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"Progreso a lo largo del tiempo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this),\n                                    estadisticas.progresoTiempo.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-4\",\n                                        children: \"No hay datos de progreso disponibles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"min-w-full divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Fecha\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Nuevas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Aprendiendo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Repasando\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                children: \"Aprendidas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"bg-white divide-y divide-gray-200\",\n                                                    children: estadisticas.progresoTiempo.map((progreso, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                    children: formatearFecha(progreso.fecha)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                                                                        children: progreso.nuevas\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800\",\n                                                                        children: progreso.aprendiendo\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                                        children: progreso.repasando\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\",\n                                                                        children: progreso.aprendidas\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 29\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this),\n                            activeTab === 'dificiles' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-3\",\n                                        children: \"Tarjetas m\\xe1s dif\\xedciles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 19\n                                    }, this),\n                                    estadisticas.tarjetasMasDificiles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 text-center py-4\",\n                                        children: \"No hay datos suficientes para determinar las tarjetas m\\xe1s dif\\xedciles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: estadisticas.tarjetasMasDificiles.map((tarjeta)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium mb-2\",\n                                                        children: tarjeta.pregunta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block w-3 h-3 rounded-full bg-red-500 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Dif\\xedcil: \",\n                                                                            tarjeta.dificil\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"Normal: \",\n                                                                            tarjeta.normal\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-block w-3 h-3 rounded-full bg-green-500 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"F\\xe1cil: \",\n                                                                            tarjeta.facil\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-1.5 flex\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-red-500 h-1.5 rounded-l-full\",\n                                                                    style: {\n                                                                        width: \"\".concat(calcularPorcentaje(tarjeta.dificil, tarjeta.totalRevisiones), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-yellow-500 h-1.5\",\n                                                                    style: {\n                                                                        width: \"\".concat(calcularPorcentaje(tarjeta.normal, tarjeta.totalRevisiones), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-green-500 h-1.5 rounded-r-full\",\n                                                                    style: {\n                                                                        width: \"\".concat(calcularPorcentaje(tarjeta.facil, tarjeta.totalRevisiones), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, tarjeta.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\StudyStatistics.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(StudyStatistics, \"8OjD9uVN6aD9eFxTAReAro2J9xM=\");\n_c = StudyStatistics;\nvar _c;\n$RefreshReg$(_c, \"StudyStatistics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StudyStatistics.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardCollectionView.tsx":
/*!***************************************************************!*\
  !*** ./src/components/flashcards/FlashcardCollectionView.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst FlashcardCollectionView = (param)=>{\n    let { coleccion, flashcards, estadisticas, isLoading, onStartStudy, onShowStudyOptions, onShowStatistics } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: coleccion.titulo\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            estadisticas && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 p-4 rounded-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-2\",\n                        children: \"Estad\\xedsticas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-5 gap-2 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-blue-600\",\n                                        children: estadisticas.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Total\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-orange-600\",\n                                        children: estadisticas.paraHoy\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Para hoy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-gray-600\",\n                                        children: estadisticas.nuevas\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Nuevas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-yellow-600\",\n                                        children: estadisticas.aprendiendo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Aprendiendo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-green-600\",\n                                        children: estadisticas.aprendidas\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Aprendidas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onStartStudy,\n                        className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors\",\n                        children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onShowStudyOptions,\n                        className: \"bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors\",\n                        children: \"Opciones de estudio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onShowStatistics,\n                        className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:shadow-outline transition-colors\",\n                        children: \"Ver estad\\xedsticas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                children: flashcards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border rounded-lg \".concat(card.debeEstudiar ? 'border-orange-300 bg-orange-50' : ''),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: card.pregunta\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"Tarjeta \",\n                                            index + 1\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    card.progreso && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs px-2 py-1 rounded-full \".concat(card.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : card.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : card.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                        children: card.progreso.estado\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, card.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardCollectionView.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardCollectionView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardCollectionView);\nvar _c;\n$RefreshReg$(_c, \"FlashcardCollectionView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardCollectionView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx":
/*!*************************************************************!*\
  !*** ./src/components/flashcards/FlashcardStudyOptions.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiBookOpen,FiClock,FiHelpCircle,FiLoader,FiRefreshCw,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst FlashcardStudyOptions = (param)=>{\n    let { isOpen, onClose, onSelectStudyType, estadisticas, isLoading = false } = param;\n    const opcionesEstudio = [\n        {\n            tipo: 'programadas',\n            label: 'Programadas para hoy',\n            descripcion: 'Tarjetas que deben estudiarse según el algoritmo de repetición espaciada',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                className: \"text-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 25,\n                columnNumber: 13\n            }, undefined),\n            color: 'blue-600',\n            bgColor: 'bg-blue-100',\n            hoverBgColor: 'hover:bg-blue-200'\n        },\n        {\n            tipo: 'dificiles',\n            label: 'Más difíciles',\n            descripcion: 'Tarjetas que has marcado como difíciles más frecuentemente',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiHelpCircle, {\n                className: \"text-red-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, undefined),\n            color: 'red-600',\n            bgColor: 'bg-red-100',\n            hoverBgColor: 'hover:bg-red-200'\n        },\n        {\n            tipo: 'aleatorias',\n            label: 'Aleatorias',\n            descripcion: 'Selección aleatoria de tarjetas de la colección',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                className: \"text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, undefined),\n            color: 'purple-600',\n            bgColor: 'bg-purple-100',\n            hoverBgColor: 'hover:bg-purple-200'\n        },\n        {\n            tipo: 'no-recientes',\n            label: 'No estudiadas recientemente',\n            descripcion: 'Tarjetas que no has revisado en mucho tiempo',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                className: \"text-orange-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, undefined),\n            color: 'orange-600',\n            bgColor: 'bg-orange-100',\n            hoverBgColor: 'hover:bg-orange-200'\n        },\n        {\n            tipo: 'nuevas',\n            label: 'Nuevas',\n            descripcion: 'Tarjetas que nunca has estudiado',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiBookOpen, {\n                className: \"text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, undefined),\n            color: 'green-600',\n            bgColor: 'bg-green-100',\n            hoverBgColor: 'hover:bg-green-200'\n        },\n        {\n            tipo: 'aprendiendo',\n            label: 'En aprendizaje',\n            descripcion: 'Tarjetas que estás aprendiendo actualmente',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiLoader, {\n                className: \"text-yellow-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, undefined),\n            color: 'yellow-600',\n            bgColor: 'bg-yellow-100',\n            hoverBgColor: 'hover:bg-yellow-200'\n        }\n    ];\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"Opciones de Estudio\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Elige el tipo de estudio que prefieras. Cada opci\\xf3n te permitir\\xe1 enfocar tu aprendizaje de manera diferente:\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: opcionesEstudio.map((opcion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onSelectStudyType(opcion.tipo),\n                            className: \"p-4 border rounded-lg text-left transition-all duration-200 \".concat(opcion.hoverBgColor, \" \").concat(opcion.bgColor, \" border-gray-200 hover:border-gray-300\"),\n                            disabled: isLoading,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-1\",\n                                        children: opcion.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-\".concat(opcion.color, \" mb-1\"),\n                                                children: opcion.tipo === 'programadas' ? \"\".concat(opcion.label, \" (\").concat(estadisticas ? estadisticas.paraHoy : 0, \")\") : opcion.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: opcion.descripcion\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, undefined)\n                        }, opcion.tipo, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex justify-end space-x-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                        children: \"Cancelar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardStudyOptions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardStudyOptions);\nvar _c;\n$RefreshReg$(_c, \"FlashcardStudyOptions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2ZsYXNoY2FyZHMvRmxhc2hjYXJkU3R1ZHlPcHRpb25zLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFDcUU7QUFXL0YsTUFBTU8sd0JBQThEO1FBQUMsRUFDbkVDLE1BQU0sRUFDTkMsT0FBTyxFQUNQQyxpQkFBaUIsRUFDakJDLFlBQVksRUFDWkMsWUFBWSxLQUFLLEVBQ2xCO0lBQ0MsTUFBTUMsa0JBQW1DO1FBQ3ZDO1lBQ0VDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLG9CQUFNLDhEQUFDZixtSUFBT0E7Z0JBQUNnQixXQUFVOzs7Ozs7WUFDekJDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRVAsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsb0JBQU0sOERBQUNkLHdJQUFZQTtnQkFBQ2UsV0FBVTs7Ozs7O1lBQzlCQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsY0FBYztRQUNoQjtRQUNBO1lBQ0VQLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLG9CQUFNLDhEQUFDYix1SUFBV0E7Z0JBQUNjLFdBQVU7Ozs7OztZQUM3QkMsT0FBTztZQUNQQyxTQUFTO1lBQ1RDLGNBQWM7UUFDaEI7UUFDQTtZQUNFUCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxvQkFBTSw4REFBQ2YsbUlBQU9BO2dCQUFDZ0IsV0FBVTs7Ozs7O1lBQ3pCQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsY0FBYztRQUNoQjtRQUNBO1lBQ0VQLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLG9CQUFNLDhEQUFDWixzSUFBVUE7Z0JBQUNhLFdBQVU7Ozs7OztZQUM1QkMsT0FBTztZQUNQQyxTQUFTO1lBQ1RDLGNBQWM7UUFDaEI7UUFDQTtZQUNFUCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxvQkFBTSw4REFBQ1gsb0lBQVFBO2dCQUFDWSxXQUFVOzs7Ozs7WUFDMUJDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxjQUFjO1FBQ2hCO0tBQ0Q7SUFFRCxJQUFJLENBQUNiLFFBQVEsT0FBTztJQUVwQixxQkFDRSw4REFBQ2M7UUFBSUosV0FBVTtrQkFDYiw0RUFBQ0k7WUFBSUosV0FBVTs7OEJBQ2IsOERBQUNJO29CQUFJSixXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQUdMLFdBQVU7c0NBQXdCOzs7Ozs7c0NBQ3RDLDhEQUFDTTs0QkFDQ0MsU0FBU2hCOzRCQUNUUyxXQUFVO3NDQUVWLDRFQUFDakIsK0hBQUdBO2dDQUFDeUIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSWYsOERBQUNDO29CQUFFVCxXQUFVOzhCQUFxQjs7Ozs7OzhCQUlsQyw4REFBQ0k7b0JBQUlKLFdBQVU7OEJBQ1pMLGdCQUFnQmUsR0FBRyxDQUFDLENBQUNDLHVCQUNwQiw4REFBQ0w7NEJBRUNDLFNBQVMsSUFBTWYsa0JBQWtCbUIsT0FBT2YsSUFBSTs0QkFDNUNJLFdBQVcsK0RBQXNGVyxPQUF2QkEsT0FBT1IsWUFBWSxFQUFDLEtBQWtCLE9BQWZRLE9BQU9ULE9BQU8sRUFBQzs0QkFDaEhVLFVBQVVsQjtzQ0FFViw0RUFBQ1U7Z0NBQUlKLFdBQVU7O2tEQUNiLDhEQUFDSTt3Q0FBSUosV0FBVTtrREFDWlcsT0FBT1osSUFBSTs7Ozs7O2tEQUVkLDhEQUFDSzt3Q0FBSUosV0FBVTs7MERBQ2IsOERBQUNhO2dEQUFHYixXQUFXLG9CQUFpQyxPQUFiVyxPQUFPVixLQUFLLEVBQUM7MERBQzdDVSxPQUFPZixJQUFJLEtBQUssZ0JBQ2IsR0FBb0JILE9BQWpCa0IsT0FBT2QsS0FBSyxFQUFDLE1BQTRDLE9BQXhDSixlQUFlQSxhQUFhcUIsT0FBTyxHQUFHLEdBQUUsT0FDNURILE9BQU9kLEtBQUs7Ozs7OzswREFHbEIsOERBQUNZO2dEQUFFVCxXQUFVOzBEQUNWVyxPQUFPYixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBakJwQmEsT0FBT2YsSUFBSTs7Ozs7Ozs7Ozs4QkF5QnRCLDhEQUFDUTtvQkFBSUosV0FBVTs4QkFDYiw0RUFBQ007d0JBQ0NDLFNBQVNoQjt3QkFDVFMsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YO0tBMUhNWDtBQTRITixpRUFBZUEscUJBQXFCQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxjb21wb25lbnRzXFxmbGFzaGNhcmRzXFxGbGFzaGNhcmRTdHVkeU9wdGlvbnMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBGaVgsIEZpQ2xvY2ssIEZpSGVscENpcmNsZSwgRmlSZWZyZXNoQ3csIEZpQm9va09wZW4sIEZpTG9hZGVyIH0gZnJvbSAncmVhY3QtaWNvbnMvZmknO1xuaW1wb3J0IHsgVGlwb0VzdHVkaW8sIE9wY2lvbkVzdHVkaW8sIEVzdGFkaXN0aWNhc0NvbGVjY2lvbiB9IGZyb20gJy4vdHlwZXMnO1xuXG5pbnRlcmZhY2UgRmxhc2hjYXJkU3R1ZHlPcHRpb25zUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW47XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIG9uU2VsZWN0U3R1ZHlUeXBlOiAodGlwbzogVGlwb0VzdHVkaW8pID0+IHZvaWQ7XG4gIGVzdGFkaXN0aWNhcz86IEVzdGFkaXN0aWNhc0NvbGVjY2lvbiB8IG51bGw7XG4gIGlzTG9hZGluZz86IGJvb2xlYW47XG59XG5cbmNvbnN0IEZsYXNoY2FyZFN0dWR5T3B0aW9uczogUmVhY3QuRkM8Rmxhc2hjYXJkU3R1ZHlPcHRpb25zUHJvcHM+ID0gKHtcbiAgaXNPcGVuLFxuICBvbkNsb3NlLFxuICBvblNlbGVjdFN0dWR5VHlwZSxcbiAgZXN0YWRpc3RpY2FzLFxuICBpc0xvYWRpbmcgPSBmYWxzZVxufSkgPT4ge1xuICBjb25zdCBvcGNpb25lc0VzdHVkaW86IE9wY2lvbkVzdHVkaW9bXSA9IFtcbiAgICB7XG4gICAgICB0aXBvOiAncHJvZ3JhbWFkYXMnLFxuICAgICAgbGFiZWw6ICdQcm9ncmFtYWRhcyBwYXJhIGhveScsXG4gICAgICBkZXNjcmlwY2lvbjogJ1RhcmpldGFzIHF1ZSBkZWJlbiBlc3R1ZGlhcnNlIHNlZ8O6biBlbCBhbGdvcml0bW8gZGUgcmVwZXRpY2nDs24gZXNwYWNpYWRhJyxcbiAgICAgIGljb246IDxGaUNsb2NrIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIiAvPixcbiAgICAgIGNvbG9yOiAnYmx1ZS02MDAnLFxuICAgICAgYmdDb2xvcjogJ2JnLWJsdWUtMTAwJyxcbiAgICAgIGhvdmVyQmdDb2xvcjogJ2hvdmVyOmJnLWJsdWUtMjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGlwbzogJ2RpZmljaWxlcycsXG4gICAgICBsYWJlbDogJ03DoXMgZGlmw61jaWxlcycsXG4gICAgICBkZXNjcmlwY2lvbjogJ1RhcmpldGFzIHF1ZSBoYXMgbWFyY2FkbyBjb21vIGRpZsOtY2lsZXMgbcOhcyBmcmVjdWVudGVtZW50ZScsXG4gICAgICBpY29uOiA8RmlIZWxwQ2lyY2xlIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMFwiIC8+LFxuICAgICAgY29sb3I6ICdyZWQtNjAwJyxcbiAgICAgIGJnQ29sb3I6ICdiZy1yZWQtMTAwJyxcbiAgICAgIGhvdmVyQmdDb2xvcjogJ2hvdmVyOmJnLXJlZC0yMDAnXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXBvOiAnYWxlYXRvcmlhcycsXG4gICAgICBsYWJlbDogJ0FsZWF0b3JpYXMnLFxuICAgICAgZGVzY3JpcGNpb246ICdTZWxlY2Npw7NuIGFsZWF0b3JpYSBkZSB0YXJqZXRhcyBkZSBsYSBjb2xlY2Npw7NuJyxcbiAgICAgIGljb246IDxGaVJlZnJlc2hDdyBjbGFzc05hbWU9XCJ0ZXh0LXB1cnBsZS02MDBcIiAvPixcbiAgICAgIGNvbG9yOiAncHVycGxlLTYwMCcsXG4gICAgICBiZ0NvbG9yOiAnYmctcHVycGxlLTEwMCcsXG4gICAgICBob3ZlckJnQ29sb3I6ICdob3ZlcjpiZy1wdXJwbGUtMjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGlwbzogJ25vLXJlY2llbnRlcycsXG4gICAgICBsYWJlbDogJ05vIGVzdHVkaWFkYXMgcmVjaWVudGVtZW50ZScsXG4gICAgICBkZXNjcmlwY2lvbjogJ1RhcmpldGFzIHF1ZSBubyBoYXMgcmV2aXNhZG8gZW4gbXVjaG8gdGllbXBvJyxcbiAgICAgIGljb246IDxGaUNsb2NrIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTYwMFwiIC8+LFxuICAgICAgY29sb3I6ICdvcmFuZ2UtNjAwJyxcbiAgICAgIGJnQ29sb3I6ICdiZy1vcmFuZ2UtMTAwJyxcbiAgICAgIGhvdmVyQmdDb2xvcjogJ2hvdmVyOmJnLW9yYW5nZS0yMDAnXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXBvOiAnbnVldmFzJyxcbiAgICAgIGxhYmVsOiAnTnVldmFzJyxcbiAgICAgIGRlc2NyaXBjaW9uOiAnVGFyamV0YXMgcXVlIG51bmNhIGhhcyBlc3R1ZGlhZG8nLFxuICAgICAgaWNvbjogPEZpQm9va09wZW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDBcIiAvPixcbiAgICAgIGNvbG9yOiAnZ3JlZW4tNjAwJyxcbiAgICAgIGJnQ29sb3I6ICdiZy1ncmVlbi0xMDAnLFxuICAgICAgaG92ZXJCZ0NvbG9yOiAnaG92ZXI6YmctZ3JlZW4tMjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGlwbzogJ2FwcmVuZGllbmRvJyxcbiAgICAgIGxhYmVsOiAnRW4gYXByZW5kaXphamUnLFxuICAgICAgZGVzY3JpcGNpb246ICdUYXJqZXRhcyBxdWUgZXN0w6FzIGFwcmVuZGllbmRvIGFjdHVhbG1lbnRlJyxcbiAgICAgIGljb246IDxGaUxvYWRlciBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy02MDBcIiAvPixcbiAgICAgIGNvbG9yOiAneWVsbG93LTYwMCcsXG4gICAgICBiZ0NvbG9yOiAnYmcteWVsbG93LTEwMCcsXG4gICAgICBob3ZlckJnQ29sb3I6ICdob3ZlcjpiZy15ZWxsb3ctMjAwJ1xuICAgIH1cbiAgXTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgcC02IG1heC13LTJ4bCB3LWZ1bGwgbXgtNCBtYXgtaC1bODB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+T3BjaW9uZXMgZGUgRXN0dWRpbzwvaDM+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEZpWCBzaXplPXsyNH0gLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgRWxpZ2UgZWwgdGlwbyBkZSBlc3R1ZGlvIHF1ZSBwcmVmaWVyYXMuIENhZGEgb3BjacOzbiB0ZSBwZXJtaXRpcsOhIGVuZm9jYXIgdHUgYXByZW5kaXphamUgZGUgbWFuZXJhIGRpZmVyZW50ZTpcbiAgICAgICAgPC9wPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgIHtvcGNpb25lc0VzdHVkaW8ubWFwKChvcGNpb24pID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXtvcGNpb24udGlwb31cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25TZWxlY3RTdHVkeVR5cGUob3BjaW9uLnRpcG8pfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTQgYm9yZGVyIHJvdW5kZWQtbGcgdGV4dC1sZWZ0IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke29wY2lvbi5ob3ZlckJnQ29sb3J9ICR7b3BjaW9uLmJnQ29sb3J9IGJvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDBgfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIHtvcGNpb24uaWNvbn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtIHRleHQtJHtvcGNpb24uY29sb3J9IG1iLTFgfT5cbiAgICAgICAgICAgICAgICAgICAge29wY2lvbi50aXBvID09PSAncHJvZ3JhbWFkYXMnXG4gICAgICAgICAgICAgICAgICAgICAgPyBgJHtvcGNpb24ubGFiZWx9ICgke2VzdGFkaXN0aWNhcyA/IGVzdGFkaXN0aWNhcy5wYXJhSG95IDogMH0pYFxuICAgICAgICAgICAgICAgICAgICAgIDogb3BjaW9uLmxhYmVsXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge29wY2lvbi5kZXNjcmlwY2lvbn1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IGZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS04MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENhbmNlbGFyXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGbGFzaGNhcmRTdHVkeU9wdGlvbnM7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJGaVgiLCJGaUNsb2NrIiwiRmlIZWxwQ2lyY2xlIiwiRmlSZWZyZXNoQ3ciLCJGaUJvb2tPcGVuIiwiRmlMb2FkZXIiLCJGbGFzaGNhcmRTdHVkeU9wdGlvbnMiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TZWxlY3RTdHVkeVR5cGUiLCJlc3RhZGlzdGljYXMiLCJpc0xvYWRpbmciLCJvcGNpb25lc0VzdHVkaW8iLCJ0aXBvIiwibGFiZWwiLCJkZXNjcmlwY2lvbiIsImljb24iLCJjbGFzc05hbWUiLCJjb2xvciIsImJnQ29sb3IiLCJob3ZlckJnQ29sb3IiLCJkaXYiLCJoMyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzaXplIiwicCIsIm1hcCIsIm9wY2lvbiIsImRpc2FibGVkIiwiaDQiLCJwYXJhSG95Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx\n"));

/***/ })

});