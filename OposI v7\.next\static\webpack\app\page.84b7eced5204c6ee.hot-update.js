"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FlashcardViewer.tsx":
/*!********************************************!*\
  !*** ./src/components/FlashcardViewer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _flashcards_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./flashcards/FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _flashcards_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./flashcards/FlashcardCollectionView */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionView.tsx\");\n/* harmony import */ var _flashcards_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./flashcards/FlashcardStudyOptions */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx\");\n/* harmony import */ var _flashcards_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./flashcards/FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _StudyStatistics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./StudyStatistics */ \"(app-pages-browser)/./src/components/StudyStatistics.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction FlashcardViewer() {\n    _s();\n    // Estados principales\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingColecciones, setIsLoadingColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales y modos\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticas, setMostrarEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoadingColecciones(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoadingColecciones(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const iniciarModoEstudio = async function() {\n        let tipoEstudio = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'programadas';\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                let flashcardsParaEstudiar = [];\n                // Obtener flashcards según el tipo de estudio seleccionado\n                switch(tipoEstudio){\n                    case 'programadas':\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                        break;\n                    case 'dificiles':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsMasDificiles)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'aleatorias':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsAleatorias)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'no-recientes':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsNoRecientes)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'nuevas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'nuevo', 20);\n                        break;\n                    case 'aprendiendo':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendiendo', 20);\n                        break;\n                    case 'repasando':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'repasando', 20);\n                        break;\n                    case 'aprendidas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendido', 20);\n                        break;\n                    default:\n                        const defaultData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = defaultData.filter((flashcard)=>flashcard.debeEstudiar);\n                }\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Si no hay flashcards para el tipo seleccionado, mostrar mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (tipoEstudio === 'programadas') {\n                        alert('No hay flashcards programadas para estudiar hoy. Puedes usar \"Opciones de estudio\" para elegir otro tipo de repaso.');\n                        return;\n                    } else {\n                        alert(\"No hay flashcards disponibles para el tipo de estudio seleccionado.\");\n                        return;\n                    }\n                }\n                // Usar las flashcards obtenidas\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarOpcionesEstudio(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo estudio:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const salirModoEstudio = async ()=>{\n        setModoEstudio(false);\n        // Recargar las flashcards y estadísticas\n        if (coleccionSeleccionada) {\n            try {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const ordenadas = [\n                    ...data\n                ].sort((a, b)=>{\n                    if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                    if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                    return 0;\n                });\n                setFlashcards(ordenadas);\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n            } catch (error) {\n                console.error('Error al recargar datos:', error);\n            }\n        }\n    };\n    const handleRespuesta = async (dificultad)=>{\n        if (!flashcards[activeIndex]) return;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcards[activeIndex].id, dificultad);\n            if (!exito) {\n                throw new Error('Error al registrar la respuesta');\n            }\n            // Si es la última tarjeta, terminar la sesión\n            if (activeIndex >= flashcards.length - 1) {\n                alert('¡Has completado la sesión de estudio!');\n                await salirModoEstudio();\n            } else {\n                // Avanzar a la siguiente tarjeta\n                setActiveIndex(activeIndex + 1);\n            }\n        } catch (error) {\n            console.error('Error al actualizar progreso:', error);\n            setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');\n        } finally{\n            setRespondiendo(false);\n        }\n    };\n    const handleNavigate = (direction)=>{\n        if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n        } else if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n        }\n    };\n    const handleEliminarColeccion = async (coleccionId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingId(coleccionId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (exito) {\n                // Recargar las colecciones\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                setColecciones(data);\n                // Si la colección eliminada era la seleccionada, deseleccionarla\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                setError('No se pudo eliminar la colección');\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            setError('Error al eliminar la colección');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: salirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionSeleccionada ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoadingColecciones,\n                        deletingId: deletingId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setColeccionSeleccionada(null),\n                                className: \"mb-4 text-blue-600 hover:text-blue-800 flex items-center\",\n                                children: \"← Volver a mis colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                coleccion: coleccionSeleccionada,\n                                flashcards: flashcards,\n                                estadisticas: estadisticas,\n                                isLoading: isLoading,\n                                onStartStudy: ()=>iniciarModoEstudio('programadas'),\n                                onShowStudyOptions: ()=>setMostrarOpcionesEstudio(true),\n                                onShowStatistics: ()=>setMostrarEstadisticas(true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: mostrarOpcionesEstudio,\n                onClose: ()=>setMostrarOpcionesEstudio(false),\n                onSelectStudyType: iniciarModoEstudio,\n                estadisticas: estadisticas,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            mostrarEstadisticas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudyStatistics__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                onClose: ()=>setMostrarEstadisticas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardViewer, \"ydFcPtLOP5ghLJe9pJKQEAkrVf4=\");\n_c = FlashcardViewer;\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FlashcardViewer.tsx\n"));

/***/ })

});