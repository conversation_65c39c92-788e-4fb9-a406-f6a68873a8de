"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/hooks/useAutoLogout.ts":
/*!************************************!*\
  !*** ./src/hooks/useAutoLogout.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\nconst useAutoLogout = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { timeoutMinutes = 5, warningMinutes = 0.5, enabled = true } = options;\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const warningTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const warningShownRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convertir minutos a milisegundos\n    const timeoutMs = timeoutMinutes * 60 * 1000;\n    const warningMs = warningMinutes * 60 * 1000;\n    const clearTimeouts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[clearTimeouts]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            if (warningTimeoutRef.current) {\n                clearTimeout(warningTimeoutRef.current);\n                warningTimeoutRef.current = null;\n            }\n        }\n    }[\"useAutoLogout.useCallback[clearTimeouts]\"], []);\n    const handleLogout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleLogout]\": async ()=>{\n            try {\n                // Limpiar timeouts antes del logout\n                clearTimeouts();\n                await logout();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Sesión cerrada por inactividad', {\n                    duration: 4000,\n                    position: 'top-center',\n                    id: 'auto-logout' // Evitar duplicados\n                });\n            } catch (error) {\n                console.error('Error al cerrar sesión por inactividad:', error);\n                // Intentar logout forzado si hay error\n                try {\n                    window.location.href = '/login';\n                } catch (redirectError) {\n                    console.error('Error al redirigir:', redirectError);\n                }\n            }\n        }\n    }[\"useAutoLogout.useCallback[handleLogout]\"], [\n        logout,\n        clearTimeouts\n    ]);\n    const showWarning = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[showWarning]\": ()=>{\n            if (!warningShownRef.current) {\n                warningShownRef.current = true;\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.warning('⚠️ Tu sesión se cerrará en 30 segundos por inactividad', {\n                    duration: 4000,\n                    position: 'top-center',\n                    id: 'auto-logout-warning',\n                    style: {\n                        background: '#f59e0b',\n                        color: '#fff',\n                        fontWeight: 'bold'\n                    }\n                });\n            }\n        }\n    }[\"useAutoLogout.useCallback[showWarning]\"], []);\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n            if (!enabled || !user) return;\n            lastActivityRef.current = Date.now();\n            warningShownRef.current = false;\n            clearTimeouts();\n            // Configurar timeout para mostrar advertencia\n            if (warningMs > 0) {\n                warningTimeoutRef.current = setTimeout({\n                    \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                        showWarning();\n                    }\n                }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs - warningMs);\n            }\n            // Configurar timeout para logout\n            timeoutRef.current = setTimeout({\n                \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                    handleLogout();\n                }\n            }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs);\n        }\n    }[\"useAutoLogout.useCallback[resetTimer]\"], [\n        enabled,\n        user,\n        timeoutMs,\n        warningMs,\n        clearTimeouts,\n        handleLogout,\n        showWarning\n    ]);\n    // Eventos que indican actividad del usuario\n    const activityEvents = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click'\n    ];\n    const handleActivity = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleActivity]\": (event)=>{\n            // Evitar resetear el timer por movimientos mínimos del mouse\n            if (event.type === 'mousemove') {\n                const now = Date.now();\n                if (now - lastActivityRef.current < 1000) {\n                    return;\n                }\n            }\n            resetTimer();\n        }\n    }[\"useAutoLogout.useCallback[handleActivity]\"], [\n        resetTimer\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            if (!enabled || !user) {\n                clearTimeouts();\n                return;\n            }\n            // Inicializar timer\n            resetTimer();\n            // Agregar event listeners\n            activityEvents.forEach({\n                \"useAutoLogout.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useAutoLogout.useEffect\"]);\n            // Cleanup\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                    activityEvents.forEach({\n                        \"useAutoLogout.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useAutoLogout.useEffect\"]);\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        enabled,\n        user,\n        resetTimer,\n        handleActivity,\n        clearTimeouts\n    ]);\n    // Cleanup al desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        clearTimeouts\n    ]);\n    return {\n        resetTimer,\n        clearTimeouts,\n        isEnabled: enabled && !!user\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoLogout);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoLogout.ts\n"));

/***/ })

});