"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FlashcardViewer.tsx":
/*!********************************************!*\
  !*** ./src/components/FlashcardViewer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _flashcards_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./flashcards/FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _flashcards_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./flashcards/FlashcardCollectionView */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionView.tsx\");\n/* harmony import */ var _flashcards_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./flashcards/FlashcardStudyOptions */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx\");\n/* harmony import */ var _flashcards_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./flashcards/FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _StudyStatistics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./StudyStatistics */ \"(app-pages-browser)/./src/components/StudyStatistics.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction FlashcardViewer() {\n    _s();\n    // Estados principales\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingColecciones, setIsLoadingColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modales y modos\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticas, setMostrarEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoadingColecciones(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoadingColecciones(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const iniciarModoEstudio = async function() {\n        let tipoEstudio = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'programadas';\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                let flashcardsParaEstudiar = [];\n                // Obtener flashcards según el tipo de estudio seleccionado\n                switch(tipoEstudio){\n                    case 'programadas':\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                        break;\n                    case 'dificiles':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsMasDificiles)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'aleatorias':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsAleatorias)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'no-recientes':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsNoRecientes)(coleccionSeleccionada.id, 20);\n                        break;\n                    case 'nuevas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'nuevo', 20);\n                        break;\n                    case 'aprendiendo':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendiendo', 20);\n                        break;\n                    case 'repasando':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'repasando', 20);\n                        break;\n                    case 'aprendidas':\n                        flashcardsParaEstudiar = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsPorEstado)(coleccionSeleccionada.id, 'aprendido', 20);\n                        break;\n                    default:\n                        const defaultData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                        flashcardsParaEstudiar = defaultData.filter((flashcard)=>flashcard.debeEstudiar);\n                }\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Si no hay flashcards para el tipo seleccionado, mostrar mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (tipoEstudio === 'programadas') {\n                        alert('No hay flashcards programadas para estudiar hoy. Puedes usar \"Opciones de estudio\" para elegir otro tipo de repaso.');\n                        return;\n                    } else {\n                        alert(\"No hay flashcards disponibles para el tipo de estudio seleccionado.\");\n                        return;\n                    }\n                }\n                // Usar las flashcards obtenidas\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarOpcionesEstudio(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo estudio:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const salirModoEstudio = async ()=>{\n        setModoEstudio(false);\n        // Recargar las flashcards y estadísticas\n        if (coleccionSeleccionada) {\n            try {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const ordenadas = [\n                    ...data\n                ].sort((a, b)=>{\n                    if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                    if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                    return 0;\n                });\n                setFlashcards(ordenadas);\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n            } catch (error) {\n                console.error('Error al recargar datos:', error);\n            }\n        }\n    };\n    const handleRespuesta = async (dificultad)=>{\n        if (!flashcards[activeIndex]) return;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcards[activeIndex].id, dificultad);\n            if (!exito) {\n                throw new Error('Error al registrar la respuesta');\n            }\n            // Si es la última tarjeta, terminar la sesión\n            if (activeIndex >= flashcards.length - 1) {\n                alert('¡Has completado la sesión de estudio!');\n                await salirModoEstudio();\n            } else {\n                // Avanzar a la siguiente tarjeta\n                setActiveIndex(activeIndex + 1);\n            }\n        } catch (error) {\n            console.error('Error al actualizar progreso:', error);\n            setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');\n        } finally{\n            setRespondiendo(false);\n        }\n    };\n    const handleNavigate = (direction)=>{\n        if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n        } else if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n        }\n    };\n    const handleEliminarColeccion = async (coleccionId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {\n            return;\n        }\n        setDeletingId(coleccionId);\n        try {\n            const exito = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (exito) {\n                // Recargar las colecciones\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                setColecciones(data);\n                // Si la colección eliminada era la seleccionada, deseleccionarla\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                setError('No se pudo eliminar la colección');\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            setError('Error al eliminar la colección');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: salirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart2_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    !coleccionSeleccionada ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: isLoadingColecciones,\n                        deletingId: deletingId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setColeccionSeleccionada(null),\n                                className: \"mb-4 text-blue-600 hover:text-blue-800 flex items-center\",\n                                children: \"← Volver a mis colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardCollectionView__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                coleccion: coleccionSeleccionada,\n                                flashcards: flashcards,\n                                estadisticas: estadisticas,\n                                isLoading: isLoading,\n                                onStartStudy: ()=>iniciarModoEstudio('programadas'),\n                                onShowStudyOptions: ()=>setMostrarOpcionesEstudio(true),\n                                onShowStatistics: ()=>setMostrarEstadisticas(true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_flashcards_FlashcardStudyOptions__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: mostrarOpcionesEstudio,\n                onClose: ()=>setMostrarOpcionesEstudio(false),\n                onSelectStudyType: iniciarModoEstudio,\n                estadisticas: estadisticas,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            mostrarEstadisticas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudyStatistics__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                onClose: ()=>setMostrarEstadisticas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\FlashcardViewer.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardViewer, \"ydFcPtLOP5ghLJe9pJKQEAkrVf4=\");\n_c = FlashcardViewer;\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FlashcardViewer.tsx\n"));

/***/ })

});