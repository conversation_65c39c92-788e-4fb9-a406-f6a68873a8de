import React, { useState } from 'react';
import { FiSearch, FiDatabase, FiAlertTriangle } from 'react-icons/fi';
import { diagnosticarColeccionesFlashcards, buscarColeccionPorNombre } from '../utils/diagnosticoSupabase';

const DiagnosticoSupabase: React.FC = () => {
  const [ejecutando, setEjecutando] = useState(false);
  const [resultados, setResultados] = useState<string[]>([]);

  const ejecutarDiagnostico = async () => {
    setEjecutando(true);
    setResultados([]);
    
    // Capturar console.log para mostrar en la interfaz
    const originalLog = console.log;
    const originalError = console.error;
    const logs: string[] = [];
    
    console.log = (...args) => {
      const mensaje = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      logs.push(`[LOG] ${mensaje}`);
      originalLog(...args);
    };
    
    console.error = (...args) => {
      const mensaje = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      logs.push(`[ERROR] ${mensaje}`);
      originalError(...args);
    };
    
    try {
      await diagnosticarColeccionesFlashcards();
    } catch (error) {
      logs.push(`[ERROR] Error en diagnóstico: ${error}`);
    }
    
    // Restaurar console original
    console.log = originalLog;
    console.error = originalError;
    
    setResultados(logs);
    setEjecutando(false);
  };

  const buscarConstitucion = async () => {
    setEjecutando(true);
    
    try {
      const resultados = await buscarColeccionPorNombre('constitución');
      console.log('Resultados búsqueda Constitución:', resultados);
      
      if (resultados && resultados.length > 0) {
        setResultados([
          'Colecciones encontradas con "constitución":',
          ...resultados.map(c => `- ${c.titulo} (ID: ${c.id})`)
        ]);
      } else {
        setResultados(['No se encontraron colecciones con "constitución"']);
      }
    } catch (error) {
      setResultados([`Error en búsqueda: ${error}`]);
    }
    
    setEjecutando(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <FiDatabase className="text-blue-600 mr-3 text-2xl" />
        <h2 className="text-2xl font-bold text-gray-900">Diagnóstico de Supabase</h2>
      </div>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <FiAlertTriangle className="text-yellow-600 mr-2 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">Herramienta de Diagnóstico</h3>
            <p className="text-sm text-yellow-700 mt-1">
              Esta herramienta verifica las colecciones y flashcards en Supabase para identificar posibles problemas.
            </p>
          </div>
        </div>
      </div>

      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={ejecutarDiagnostico}
          disabled={ejecutando}
          className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-bold py-2 px-4 rounded flex items-center"
        >
          <FiDatabase className="mr-2" />
          {ejecutando ? 'Ejecutando...' : 'Diagnóstico Completo'}
        </button>
        
        <button
          onClick={buscarConstitucion}
          disabled={ejecutando}
          className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white font-bold py-2 px-4 rounded flex items-center"
        >
          <FiSearch className="mr-2" />
          {ejecutando ? 'Buscando...' : 'Buscar "Constitución"'}
        </button>
      </div>

      {resultados.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Resultados del Diagnóstico:</h3>
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {resultados.map((resultado, index) => (
              <div key={index} className={`mb-1 ${
                resultado.startsWith('[ERROR]') ? 'text-red-400' : 
                resultado.startsWith('[LOG]') ? 'text-green-400' : 'text-white'
              }`}>
                {resultado}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DiagnosticoSupabase;
