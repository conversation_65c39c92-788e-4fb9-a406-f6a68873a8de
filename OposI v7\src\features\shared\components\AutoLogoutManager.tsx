'use client';

import React from 'react';
import { useAutoLogout } from '@/hooks/useAutoLogout';

/**
 * Componente que maneja el auto-logout por inactividad
 * Se ejecuta en toda la aplicación cuando el usuario está autenticado
 */
const AutoLogoutManager: React.FC = () => {
  // Configurar auto-logout con 5 minutos de inactividad
  useAutoLogout({
    timeoutMinutes: 5,        // 5 minutos de inactividad
    warningMinutes: 0.5,      // Advertencia 30 segundos antes
    enabled: true             // Habilitado por defecto
  });

  // Este componente no renderiza nada visible
  return null;
};

export default AutoLogoutManager;
