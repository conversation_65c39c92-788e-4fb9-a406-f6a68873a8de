"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/diagnosticoSupabase.ts":
/*!******************************************!*\
  !*** ./src/utils/diagnosticoSupabase.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buscarColeccionPorNombre: () => (/* binding */ buscarColeccionPorNombre),\n/* harmony export */   diagnosticarColeccionConstitucion: () => (/* binding */ diagnosticarColeccionConstitucion),\n/* harmony export */   diagnosticarColeccionesFlashcards: () => (/* binding */ diagnosticarColeccionesFlashcards)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/supabase/authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Función de diagnóstico para verificar colecciones y flashcards en Supabase\n */ async function diagnosticarColeccionesFlashcards() {\n    console.log('=== DIAGNÓSTICO DE COLECCIONES Y FLASHCARDS ===');\n    try {\n        // 1. Verificar usuario actual\n        const { user, error: userError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (userError) {\n            console.error('❌ Error al obtener usuario:', userError);\n            return;\n        }\n        if (!user) {\n            console.error('❌ No hay usuario autenticado');\n            return;\n        }\n        console.log('✅ Usuario autenticado:', user.id, user.email);\n        // 2. Obtener todas las colecciones del usuario\n        const { data: colecciones, error: coleccionesError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (coleccionesError) {\n            console.error('❌ Error al obtener colecciones:', coleccionesError);\n            return;\n        }\n        console.log(\"\\n\\uD83D\\uDCDA COLECCIONES ENCONTRADAS: \".concat((colecciones === null || colecciones === void 0 ? void 0 : colecciones.length) || 0));\n        if (!colecciones || colecciones.length === 0) {\n            console.log('⚠️ No se encontraron colecciones para este usuario');\n            return;\n        }\n        // 3. Mostrar información de cada colección\n        for (const coleccion of colecciones){\n            console.log('\\n--- COLECCI\\xd3N: \"'.concat(coleccion.titulo, '\" ---'));\n            console.log(\"ID: \".concat(coleccion.id));\n            console.log(\"Descripci\\xf3n: \".concat(coleccion.descripcion || 'Sin descripción'));\n            console.log(\"Creada: \".concat(new Date(coleccion.creado_en).toLocaleString()));\n            console.log(\"User ID: \".concat(coleccion.user_id));\n            // Verificar flashcards de esta colección\n            const { data: flashcards, error: flashcardsError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccion.id).order('creado_en', {\n                ascending: true\n            });\n            if (flashcardsError) {\n                console.error('❌ Error al obtener flashcards para \"'.concat(coleccion.titulo, '\":'), flashcardsError);\n                continue;\n            }\n            console.log(\"\\uD83D\\uDCDD Flashcards: \".concat((flashcards === null || flashcards === void 0 ? void 0 : flashcards.length) || 0));\n            if (flashcards && flashcards.length > 0) {\n                console.log('   Primeras 3 flashcards:');\n                flashcards.slice(0, 3).forEach((fc, index)=>{\n                    console.log(\"   \".concat(index + 1, '. \"').concat(fc.pregunta.substring(0, 50), '...\"'));\n                });\n            }\n            // Verificar progreso de flashcards\n            if (flashcards && flashcards.length > 0) {\n                const { data: progresos, error: progresosError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((fc)=>fc.id));\n                if (progresosError) {\n                    console.error('❌ Error al obtener progreso para \"'.concat(coleccion.titulo, '\":'), progresosError);\n                } else {\n                    console.log(\"\\uD83D\\uDCCA Registros de progreso: \".concat((progresos === null || progresos === void 0 ? void 0 : progresos.length) || 0));\n                }\n            }\n        }\n        // 4. Buscar específicamente \"Tema 1 Constitución\"\n        console.log('\\n🔍 BÚSQUEDA ESPECÍFICA: \"Tema 1 Constitución\"');\n        const coleccionConstitucion = colecciones.find((c)=>c.titulo.toLowerCase().includes('constitución') || c.titulo.toLowerCase().includes('constitucion') || c.titulo.toLowerCase().includes('tema 1'));\n        if (coleccionConstitucion) {\n            console.log('✅ Colección encontrada:', coleccionConstitucion.titulo);\n            console.log('ID:', coleccionConstitucion.id);\n            // Verificar flashcards específicamente\n            const { data: flashcardsConstitucion, error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionConstitucion.id);\n            if (error) {\n                console.error('❌ Error al obtener flashcards de Constitución:', error);\n            } else {\n                console.log(\"\\uD83D\\uDCDD Flashcards en Constituci\\xf3n: \".concat((flashcardsConstitucion === null || flashcardsConstitucion === void 0 ? void 0 : flashcardsConstitucion.length) || 0));\n                if (flashcardsConstitucion && flashcardsConstitucion.length > 0) {\n                    console.log('Todas las flashcards:');\n                    flashcardsConstitucion.forEach((fc, index)=>{\n                        console.log(\"\".concat(index + 1, \". ID: \").concat(fc.id));\n                        console.log('   Pregunta: \"'.concat(fc.pregunta.substring(0, 100), '...\"'));\n                        console.log(\"   Creada: \".concat(new Date(fc.creado_en).toLocaleString()));\n                    });\n                } else {\n                    console.log('⚠️ No se encontraron flashcards en esta colección');\n                }\n            }\n        } else {\n            console.log('❌ No se encontró colección relacionada con \"Constitución\"');\n            console.log('Colecciones disponibles:');\n            colecciones.forEach((c)=>console.log('- \"'.concat(c.titulo, '\"')));\n        }\n        // 5. Verificar integridad de datos\n        console.log('\\n🔧 VERIFICACIÓN DE INTEGRIDAD');\n        // Verificar flashcards huérfanas (sin colección)\n        const { data: flashcardsHuerfanas, error: huerfanasError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id, coleccion_id, pregunta').not('coleccion_id', 'in', \"(\".concat(colecciones.map((c)=>\"'\".concat(c.id, \"'\")).join(','), \")\"));\n        if (huerfanasError) {\n            console.error('❌ Error al verificar flashcards huérfanas:', huerfanasError);\n        } else if (flashcardsHuerfanas && flashcardsHuerfanas.length > 0) {\n            console.log(\"⚠️ Flashcards hu\\xe9rfanas encontradas: \".concat(flashcardsHuerfanas.length));\n            flashcardsHuerfanas.forEach((fc)=>{\n                console.log(\"- ID: \".concat(fc.id, \", Colecci\\xf3n: \").concat(fc.coleccion_id, ', Pregunta: \"').concat(fc.pregunta.substring(0, 50), '...\"'));\n            });\n        } else {\n            console.log('✅ No se encontraron flashcards huérfanas');\n        }\n    } catch (error) {\n        console.error('❌ Error general en diagnóstico:', error);\n    }\n    console.log('\\n=== FIN DEL DIAGNÓSTICO ===');\n}\n/**\n * Función específica para buscar una colección por nombre\n */ async function buscarColeccionPorNombre(nombre) {\n    try {\n        const { user } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) return null;\n        const { data, error } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('user_id', user.id).ilike('titulo', \"%\".concat(nombre, \"%\"));\n        if (error) {\n            console.error('Error al buscar colección:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error en búsqueda:', error);\n        return null;\n    }\n}\n/**\n * Función específica para diagnosticar la colección \"Tema 1 constitución\"\n */ async function diagnosticarColeccionConstitucion() {\n    console.log('=== DIAGNÓSTICO ESPECÍFICO: TEMA 1 CONSTITUCIÓN ===');\n    try {\n        const { user } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('❌ No hay usuario autenticado');\n            return;\n        }\n        // ID específico de la colección según el diagnóstico anterior\n        const coleccionId = 'c25c593e-8960-47e3-9db6-c5b7849d4553';\n        console.log(\"\\uD83D\\uDD0D Analizando colecci\\xf3n ID: \".concat(coleccionId));\n        // 1. Verificar la colección\n        const { data: coleccion, error: coleccionError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', coleccionId).eq('user_id', user.id).single();\n        if (coleccionError) {\n            console.error('❌ Error al obtener colección:', coleccionError);\n            return;\n        }\n        console.log('✅ Colección encontrada:', coleccion.titulo);\n        // 2. Obtener TODAS las flashcards directamente\n        const { data: todasFlashcards, error: flashcardsError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n            ascending: true\n        });\n        if (flashcardsError) {\n            console.error('❌ Error al obtener flashcards:', flashcardsError);\n            return;\n        }\n        console.log(\"\\uD83D\\uDCDD Total flashcards en BD: \".concat((todasFlashcards === null || todasFlashcards === void 0 ? void 0 : todasFlashcards.length) || 0));\n        // 3. Obtener progreso de todas las flashcards\n        const { data: todosProgresos, error: progresosError } = await _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', (todasFlashcards === null || todasFlashcards === void 0 ? void 0 : todasFlashcards.map((fc)=>fc.id)) || []);\n        if (progresosError) {\n            console.error('❌ Error al obtener progresos:', progresosError);\n        } else {\n            console.log(\"\\uD83D\\uDCCA Total registros de progreso: \".concat((todosProgresos === null || todosProgresos === void 0 ? void 0 : todosProgresos.length) || 0));\n        }\n        // 4. Analizar flashcards sin progreso\n        const flashcardsSinProgreso = (todasFlashcards === null || todasFlashcards === void 0 ? void 0 : todasFlashcards.filter((fc)=>!(todosProgresos === null || todosProgresos === void 0 ? void 0 : todosProgresos.some((p)=>p.flashcard_id === fc.id)))) || [];\n        console.log(\"⚠️ Flashcards SIN progreso: \".concat(flashcardsSinProgreso.length));\n        if (flashcardsSinProgreso.length > 0) {\n            console.log('Primeras 5 flashcards sin progreso:');\n            flashcardsSinProgreso.slice(0, 5).forEach((fc, index)=>{\n                console.log(\"\".concat(index + 1, \". ID: \").concat(fc.id));\n                console.log('   Pregunta: \"'.concat(fc.pregunta.substring(0, 80), '...\"'));\n            });\n        }\n        // 5. Simular la función obtenerFlashcardsParaEstudiar\n        console.log('\\n🧪 SIMULANDO obtenerFlashcardsParaEstudiar():');\n        const ahora = new Date();\n        const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n        const flashcardsConProgreso = (todasFlashcards === null || todasFlashcards === void 0 ? void 0 : todasFlashcards.map((flashcard)=>{\n            const progreso = todosProgresos === null || todosProgresos === void 0 ? void 0 : todosProgresos.find((p)=>p.flashcard_id === flashcard.id);\n            if (!progreso) {\n                return {\n                    ...flashcard,\n                    debeEstudiar: true,\n                    progreso: null\n                };\n            }\n            const proximaRevision = new Date(progreso.proxima_revision);\n            const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n            const debeEstudiar = proximaRevisionSinHora <= hoy;\n            return {\n                ...flashcard,\n                debeEstudiar,\n                progreso: {\n                    factor_facilidad: progreso.factor_facilidad,\n                    intervalo: progreso.intervalo,\n                    repeticiones: progreso.repeticiones,\n                    estado: progreso.estado,\n                    proxima_revision: progreso.proxima_revision\n                }\n            };\n        })) || [];\n        console.log(\"\\uD83D\\uDCCB Flashcards procesadas: \".concat(flashcardsConProgreso.length));\n        console.log(\"\\uD83C\\uDFAF Flashcards que DEBEN estudiarse: \".concat(flashcardsConProgreso.filter((fc)=>fc.debeEstudiar).length));\n        console.log(\"⏰ Flashcards programadas para hoy: \".concat(flashcardsConProgreso.filter((fc)=>fc.debeEstudiar && fc.progreso).length));\n        console.log(\"\\uD83C\\uDD95 Flashcards nuevas (sin progreso): \".concat(flashcardsConProgreso.filter((fc)=>fc.debeEstudiar && !fc.progreso).length));\n        // 6. Verificar estadísticas\n        console.log('\\n📊 VERIFICANDO ESTADÍSTICAS:');\n        const estadisticas = {\n            total: flashcardsConProgreso.length,\n            nuevas: flashcardsConProgreso.filter((fc)=>!fc.progreso).length,\n            aprendiendo: flashcardsConProgreso.filter((fc)=>{\n                var _fc_progreso;\n                return ((_fc_progreso = fc.progreso) === null || _fc_progreso === void 0 ? void 0 : _fc_progreso.estado) === 'aprendiendo';\n            }).length,\n            repasando: flashcardsConProgreso.filter((fc)=>{\n                var _fc_progreso;\n                return ((_fc_progreso = fc.progreso) === null || _fc_progreso === void 0 ? void 0 : _fc_progreso.estado) === 'repasando';\n            }).length,\n            aprendidas: flashcardsConProgreso.filter((fc)=>{\n                var _fc_progreso;\n                return ((_fc_progreso = fc.progreso) === null || _fc_progreso === void 0 ? void 0 : _fc_progreso.estado) === 'aprendido';\n            }).length,\n            paraHoy: flashcardsConProgreso.filter((fc)=>fc.debeEstudiar).length\n        };\n        console.log('Estadísticas calculadas:');\n        console.log(\"- Total: \".concat(estadisticas.total));\n        console.log(\"- Nuevas: \".concat(estadisticas.nuevas));\n        console.log(\"- Aprendiendo: \".concat(estadisticas.aprendiendo));\n        console.log(\"- Repasando: \".concat(estadisticas.repasando));\n        console.log(\"- Aprendidas: \".concat(estadisticas.aprendidas));\n        console.log(\"- Para hoy: \".concat(estadisticas.paraHoy));\n    } catch (error) {\n        console.error('❌ Error en diagnóstico específico:', error);\n    }\n    console.log('\\n=== FIN DIAGNÓSTICO ESPECÍFICO ===');\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/diagnosticoSupabase.ts\n"));

/***/ })

});