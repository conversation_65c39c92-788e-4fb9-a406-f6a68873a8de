"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\n"));

/***/ })

});