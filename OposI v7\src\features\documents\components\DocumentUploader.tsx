import React, { useState, useRef } from 'react';
import { guardarDocumento } from '../lib/supabase';
import toast from 'react-hot-toast'; // Importar toast

interface DocumentUploaderProps {
  onSuccess?: () => void;
}

export default function DocumentUploader({ onSuccess }: DocumentUploaderProps) {
  const [titulo, setTitulo] = useState('');
  const [contenido, setContenido] = useState('');
  const [categoria, setCategoria] = useState('');
  const [numeroTema, setNumeroTema] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  // Conservamos 'mensaje' para errores de validación inline o mensajes muy específicos del formulario
  const [mensaje, setMensaje] = useState({ texto: '', tipo: '' });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMensaje({ texto: '', tipo: '' }); // Limpiar mensajes inline
    let loadingToastId: string | undefined = undefined;

    if (selectedFile) {
      loadingToastId = toast.loading(`Subiendo ${selectedFile.name}...`);
      const formData = new FormData();
      formData.append('file', selectedFile);

      // Enviar también los datos del formulario
      formData.append('titulo', titulo);
      if (categoria) formData.append('categoria', categoria);
      if (numeroTema) formData.append('numero_tema', numeroTema);

      try {
        const response = await fetch('/api/document/upload', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          toast.success(`Documento "${selectedFile.name}" subido y procesado con ID: ${result.documentId}.`, { id: loadingToastId });
          setTitulo('');
          setContenido('');
          setCategoria('');
          setNumeroTema('');
          setSelectedFile(null);
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
          if (onSuccess) {
            onSuccess();
          }
        } else {
          const errorResult = await response.json();
          toast.error(`Error al subir archivo: ${errorResult.error || response.statusText}`, { id: loadingToastId });
        }
      } catch (error) {
        console.error('Error en la subida del archivo:', error);
        toast.error('Error de conexión o inesperado al subir el archivo.', { id: loadingToastId });
      } finally {
        setIsLoading(false);
      }
    } else {
      // Fallback to manual input
      if (!titulo.trim() || !contenido.trim()) {
        setMensaje({ // Error de validación, se muestra inline
          texto: 'El título y el contenido son obligatorios si no se selecciona un archivo.',
          tipo: 'error',
        });
        setIsLoading(false);
        return;
      }

      loadingToastId = toast.loading('Guardando documento manualmente...');
      try {
        const documento = {
          titulo,
          contenido,
          categoria: categoria || undefined,
          numero_tema: numeroTema ? parseInt(numeroTema) : undefined,
        };
        const id = await guardarDocumento(documento); // Direct Supabase call
        if (id) {
          toast.success('Documento guardado manualmente correctamente.', { id: loadingToastId });
          setTitulo('');
          setContenido('');
          setCategoria('');
          setNumeroTema('');
          if (onSuccess) {
            onSuccess();
          }
        } else {
          toast.error('Error al guardar el documento manualmente.', { id: loadingToastId });
        }
      } catch (error) {
        console.error('Error al guardar documento manualmente:', error);
        toast.error('Ha ocurrido un error al guardar el documento manualmente.', { id: loadingToastId });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setMensaje({ texto: '', tipo: '' }); // Limpiar mensajes inline al cambiar archivo

    if (!file) {
      setSelectedFile(null);
      return;
    }

    const MAX_SIZE_MB = 5;
    if (file.size > MAX_SIZE_MB * 1024 * 1024) {
      toast.error(`El archivo es demasiado grande. El tamaño máximo es ${MAX_SIZE_MB}MB.`);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setSelectedFile(null);
      return;
    }

    setSelectedFile(file);
    setTitulo(file.name);

    if (file.type === 'text/plain') {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setContenido(event.target.result as string);
          toast.success('Archivo TXT leído y listo para vista previa.');
        } else {
          toast.error('Error al leer el archivo TXT.');
          setContenido('');
        }
      };
      reader.onerror = () => {
        toast.error('Error al leer el archivo TXT.');
        setContenido('');
      };
      reader.readAsText(file);
    } else if (file.type === 'application/pdf') {
      setContenido('El contenido se extraerá del PDF al guardar. Puedes editar el título si es necesario.');
      toast.success('Archivo PDF seleccionado. El contenido se procesará en el servidor.');
    } else {
      setContenido('Este tipo de archivo no tiene previsualización. El contenido se procesará en el servidor si es compatible.');
      toast(`Archivo ${file.name} seleccionado. El tipo no es previsualizable.`);
    }
  };

  return (
    <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <h2 className="text-xl font-bold mb-4">Subir nuevo documento</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="titulo" className="block text-gray-700 text-sm font-bold mb-2">
            Título:
          </label>
          <input
            type="text"
            id="titulo"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            value={titulo}
            onChange={(e) => setTitulo(e.target.value)}
            placeholder="Título del documento (se autocompleta con el nombre del archivo)"
            disabled={isLoading}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="categoria" className="block text-gray-700 text-sm font-bold mb-2">
              Categoría:
            </label>
            <select
              id="categoria"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={categoria}
              onChange={(e) => setCategoria(e.target.value)}
              disabled={isLoading}
            >
              <option value="">Seleccionar categoría</option>
              <option value="tema">Tema</option>
              <option value="anexo">Anexo</option>
              <option value="resumen">Resumen</option>
            </select>
          </div>

          <div>
            <label htmlFor="numeroTema" className="block text-gray-700 text-sm font-bold mb-2">
              Número de tema:
            </label>
            <input
              type="number"
              id="numeroTema"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={numeroTema}
              onChange={(e) => setNumeroTema(e.target.value)}
              placeholder="Opcional"
              min="1"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label htmlFor="contenido" className="block text-gray-700 text-sm font-bold mb-2">
            Contenido (manual o previsualización de .txt):
          </label>
          <textarea
            id="contenido"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={10}
            value={contenido}
            onChange={(e) => setContenido(e.target.value)}
            placeholder="Escribe o pega el contenido aquí, o selecciona un archivo .txt para previsualizarlo. Para PDFs, el contenido se extraerá automáticamente."
            disabled={isLoading}
            // required // Removed required, logic handled in handleSubmit
          />
        </div>

        <div>
          <label htmlFor="archivo" className="block text-gray-700 text-sm font-bold mb-2">
            O sube un archivo (.txt o .pdf):
          </label>
          <input
            type="file"
            id="archivo"
            ref={fileInputRef}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            onChange={handleFileUpload}
            accept=".txt,.pdf"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Solo archivos .txt o .pdf. Máximo {5}MB.
          </p>
        </div>

        {/* Mensaje inline para validaciones específicas del formulario u otra información persistente */}
        {mensaje.texto && (
          <div className={`p-3 rounded ${mensaje.tipo === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {mensaje.texto}
          </div>
        )}

        <div>
          <button
            type="submit"
            className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={isLoading}
          >
            {isLoading ? 'Guardando...' : 'Guardar documento'}
          </button>
        </div>
      </form>
    </div>
  );
}
