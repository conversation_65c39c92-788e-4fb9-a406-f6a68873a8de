"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DiagnosticoSupabase.tsx":
/*!************************************************!*\
  !*** ./src/components/DiagnosticoSupabase.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiDatabase,FiSearch!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _utils_diagnosticoSupabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/diagnosticoSupabase */ \"(app-pages-browser)/./src/utils/diagnosticoSupabase.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst DiagnosticoSupabase = ()=>{\n    _s();\n    const [ejecutando, setEjecutando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resultados, setResultados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const ejecutarDiagnostico = async ()=>{\n        setEjecutando(true);\n        setResultados([]);\n        // Capturar console.log para mostrar en la interfaz\n        const originalLog = console.log;\n        const originalError = console.error;\n        const logs = [];\n        console.log = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            const mensaje = args.map((arg)=>typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');\n            logs.push(\"[LOG] \".concat(mensaje));\n            originalLog(...args);\n        };\n        console.error = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            const mensaje = args.map((arg)=>typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');\n            logs.push(\"[ERROR] \".concat(mensaje));\n            originalError(...args);\n        };\n        try {\n            await (0,_utils_diagnosticoSupabase__WEBPACK_IMPORTED_MODULE_2__.diagnosticarColeccionesFlashcards)();\n        } catch (error) {\n            logs.push(\"[ERROR] Error en diagn\\xf3stico: \".concat(error));\n        }\n        // Restaurar console original\n        console.log = originalLog;\n        console.error = originalError;\n        setResultados(logs);\n        setEjecutando(false);\n    };\n    const buscarConstitucion = async ()=>{\n        setEjecutando(true);\n        try {\n            const resultados = await (0,_utils_diagnosticoSupabase__WEBPACK_IMPORTED_MODULE_2__.buscarColeccionPorNombre)('constitución');\n            console.log('Resultados búsqueda Constitución:', resultados);\n            if (resultados && resultados.length > 0) {\n                setResultados([\n                    'Colecciones encontradas con \"constitución\":',\n                    ...resultados.map((c)=>\"- \".concat(c.titulo, \" (ID: \").concat(c.id, \")\"))\n                ]);\n            } else {\n                setResultados([\n                    'No se encontraron colecciones con \"constitución\"'\n                ]);\n            }\n        } catch (error) {\n            setResultados([\n                \"Error en b\\xfasqueda: \".concat(error)\n            ]);\n        }\n        setEjecutando(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiDatabase, {\n                        className: \"text-blue-600 mr-3 text-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Diagn\\xf3stico de Supabase\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiAlertTriangle, {\n                            className: \"text-yellow-600 mr-2 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-yellow-800\",\n                                    children: \"Herramienta de Diagn\\xf3stico\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-700 mt-1\",\n                                    children: \"Esta herramienta verifica las colecciones y flashcards en Supabase para identificar posibles problemas.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ejecutarDiagnostico,\n                        disabled: ejecutando,\n                        className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-bold py-2 px-4 rounded flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiDatabase, {\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            ejecutando ? 'Ejecutando...' : 'Diagnóstico Completo'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: buscarConstitucion,\n                        disabled: ejecutando,\n                        className: \"bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white font-bold py-2 px-4 rounded flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiSearch, {\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            ejecutando ? 'Buscando...' : 'Buscar \"Constitución\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            resultados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-900 mb-3\",\n                        children: \"Resultados del Diagn\\xf3stico:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto\",\n                        children: resultados.map((resultado, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-1 \".concat(resultado.startsWith('[ERROR]') ? 'text-red-400' : resultado.startsWith('[LOG]') ? 'text-green-400' : 'text-white'),\n                                children: resultado\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DiagnosticoSupabase, \"bWN+TWN+o0JUHuArIdoWkr8z7x4=\");\n_c = DiagnosticoSupabase;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DiagnosticoSupabase);\nvar _c;\n$RefreshReg$(_c, \"DiagnosticoSupabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DiagnosticoSupabase.tsx\n"));

/***/ })

});