"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DiagnosticoSupabase.tsx":
/*!************************************************!*\
  !*** ./src/components/DiagnosticoSupabase.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiDatabase,FiSearch!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _utils_diagnosticoSupabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/diagnosticoSupabase */ \"(app-pages-browser)/./src/utils/diagnosticoSupabase.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst DiagnosticoSupabase = ()=>{\n    _s();\n    const [ejecutando, setEjecutando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resultados, setResultados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const ejecutarDiagnostico = async ()=>{\n        setEjecutando(true);\n        setResultados([]);\n        // Capturar console.log para mostrar en la interfaz\n        const originalLog = console.log;\n        const originalError = console.error;\n        const logs = [];\n        console.log = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            const mensaje = args.map((arg)=>typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');\n            logs.push(\"[LOG] \".concat(mensaje));\n            originalLog(...args);\n        };\n        console.error = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            const mensaje = args.map((arg)=>typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)).join(' ');\n            logs.push(\"[ERROR] \".concat(mensaje));\n            originalError(...args);\n        };\n        try {\n            await (0,_utils_diagnosticoSupabase__WEBPACK_IMPORTED_MODULE_2__.diagnosticarColeccionesFlashcards)();\n        } catch (error) {\n            logs.push(\"[ERROR] Error en diagn\\xf3stico: \".concat(error));\n        }\n        // Restaurar console original\n        console.log = originalLog;\n        console.error = originalError;\n        setResultados(logs);\n        setEjecutando(false);\n    };\n    const buscarConstitucion = async ()=>{\n        setEjecutando(true);\n        try {\n            const resultados = await (0,_utils_diagnosticoSupabase__WEBPACK_IMPORTED_MODULE_2__.buscarColeccionPorNombre)('constitución');\n            console.log('Resultados búsqueda Constitución:', resultados);\n            if (resultados && resultados.length > 0) {\n                setResultados([\n                    'Colecciones encontradas con \"constitución\":',\n                    ...resultados.map((c)=>\"- \".concat(c.titulo, \" (ID: \").concat(c.id, \")\"))\n                ]);\n            } else {\n                setResultados([\n                    'No se encontraron colecciones con \"constitución\"'\n                ]);\n            }\n        } catch (error) {\n            setResultados([\n                \"Error en b\\xfasqueda: \".concat(error)\n            ]);\n        }\n        setEjecutando(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiDatabase, {\n                        className: \"text-blue-600 mr-3 text-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Diagn\\xf3stico de Supabase\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiAlertTriangle, {\n                            className: \"text-yellow-600 mr-2 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-yellow-800\",\n                                    children: \"Herramienta de Diagn\\xf3stico\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-700 mt-1\",\n                                    children: \"Esta herramienta verifica las colecciones y flashcards en Supabase para identificar posibles problemas.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ejecutarDiagnostico,\n                        disabled: ejecutando,\n                        className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-bold py-2 px-4 rounded flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiDatabase, {\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            ejecutando ? 'Ejecutando...' : 'Diagnóstico Completo'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: buscarConstitucion,\n                        disabled: ejecutando,\n                        className: \"bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white font-bold py-2 px-4 rounded flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiDatabase_FiSearch_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiSearch, {\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            ejecutando ? 'Buscando...' : 'Buscar \"Constitución\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            resultados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-900 mb-3\",\n                        children: \"Resultados del Diagn\\xf3stico:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto\",\n                        children: resultados.map((resultado, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-1 \".concat(resultado.startsWith('[ERROR]') ? 'text-red-400' : resultado.startsWith('[LOG]') ? 'text-green-400' : 'text-white'),\n                                children: resultado\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\DiagnosticoSupabase.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DiagnosticoSupabase, \"bWN+TWN+o0JUHuArIdoWkr8z7x4=\");\n_c = DiagnosticoSupabase;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DiagnosticoSupabase);\nvar _c;\n$RefreshReg$(_c, \"DiagnosticoSupabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DiagnosticoSupabase.tsx\n"));

/***/ })

});