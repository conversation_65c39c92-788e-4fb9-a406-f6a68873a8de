import React, { useState, useEffect } from 'react';
import { FiBook, FiEdit, FiTrash2, <PERSON>Check, <PERSON>Clock, FiTrendingUp, FiCalendar } from 'react-icons/fi';
import { 
  obtenerTemarioUsuario, 
  obtenerTemas, 
  actualizarEstadoTema, 
  obtenerEstadisticasTemario 
} from '../services/temarioService';
import { Temario, Tema } from '@/lib/supabase/supabaseClient';
import { toast } from 'react-hot-toast';

const TemarioManager: React.FC = () => {
  const [temario, setTemario] = useState<Temario | null>(null);
  const [temas, setTemas] = useState<Tema[]>([]);
  const [estadisticas, setEstadisticas] = useState<{
    totalTemas: number;
    temasCompletados: number;
    porcentajeCompletado: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [actualizandoTema, setActualizandoTema] = useState<string | null>(null);

  useEffect(() => {
    cargarDatos();
  }, []);

  const cargarDatos = async () => {
    setIsLoading(true);
    try {
      const temarioData = await obtenerTemarioUsuario();
      if (temarioData) {
        setTemario(temarioData);
        
        const [temasData, estadisticasData] = await Promise.all([
          obtenerTemas(temarioData.id),
          obtenerEstadisticasTemario(temarioData.id)
        ]);
        
        setTemas(temasData);
        setEstadisticas(estadisticasData);
      }
    } catch (error) {
      console.error('Error al cargar datos del temario:', error);
      toast.error('Error al cargar el temario');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleCompletado = async (temaId: string, completado: boolean) => {
    setActualizandoTema(temaId);
    try {
      const exito = await actualizarEstadoTema(temaId, !completado);
      if (exito) {
        // Actualizar el estado local
        setTemas(temas.map(tema => 
          tema.id === temaId 
            ? { 
                ...tema, 
                completado: !completado,
                fecha_completado: !completado ? new Date().toISOString() : undefined
              }
            : tema
        ));
        
        // Recalcular estadísticas
        if (temario) {
          const nuevasEstadisticas = await obtenerEstadisticasTemario(temario.id);
          setEstadisticas(nuevasEstadisticas);
        }
        
        toast.success(!completado ? 'Tema marcado como completado' : 'Tema marcado como pendiente');
      } else {
        toast.error('Error al actualizar el estado del tema');
      }
    } catch (error) {
      console.error('Error al actualizar tema:', error);
      toast.error('Error al actualizar el tema');
    } finally {
      setActualizandoTema(null);
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!temario) {
    return (
      <div className="text-center py-12">
        <FiBook className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No hay temario configurado</h3>
        <p className="text-gray-500">Configura tu temario desde el dashboard para comenzar.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header del temario */}
      <div className="bg-white rounded-xl p-6 shadow-sm border">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{temario.titulo}</h1>
            {temario.descripcion && (
              <p className="text-gray-600">{temario.descripcion}</p>
            )}
            <div className="flex items-center mt-2 space-x-4">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                temario.tipo === 'completo' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-orange-100 text-orange-800'
              }`}>
                {temario.tipo === 'completo' ? 'Temario Completo' : 'Temas Sueltos'}
              </span>
              <span className="text-sm text-gray-500">
                Creado el {formatearFecha(temario.creado_en)}
              </span>
            </div>
          </div>
          <div className="flex space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
              <FiEdit className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Estadísticas */}
        {estadisticas && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <FiBook className="w-5 h-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Total Temas</p>
                  <p className="text-2xl font-bold text-blue-600">{estadisticas.totalTemas}</p>
                </div>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <FiCheck className="w-5 h-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-green-800">Completados</p>
                  <p className="text-2xl font-bold text-green-600">{estadisticas.temasCompletados}</p>
                </div>
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center">
                <FiTrendingUp className="w-5 h-5 text-purple-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-purple-800">Progreso</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {estadisticas.porcentajeCompletado.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Barra de progreso */}
        {estadisticas && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Progreso del temario</span>
              <span>{estadisticas.porcentajeCompletado.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${estadisticas.porcentajeCompletado}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      {/* Lista de temas */}
      <div className="bg-white rounded-xl shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Temas del Temario</h2>
          <p className="text-sm text-gray-500 mt-1">
            Marca los temas como completados según vayas estudiándolos
          </p>
        </div>
        
        <div className="divide-y divide-gray-200">
          {temas.map((tema) => (
            <div key={tema.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
                      {tema.numero}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className={`text-lg font-medium ${tema.completado ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                      {tema.titulo}
                    </h3>
                    {tema.descripcion && (
                      <p className="text-sm text-gray-600 mt-1">{tema.descripcion}</p>
                    )}
                    {tema.fecha_completado && (
                      <div className="flex items-center mt-2 text-sm text-green-600">
                        <FiCheck className="w-4 h-4 mr-1" />
                        Completado el {formatearFecha(tema.fecha_completado)}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleToggleCompletado(tema.id, tema.completado)}
                    disabled={actualizandoTema === tema.id}
                    className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                      tema.completado
                        ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    {actualizandoTema === tema.id ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1"></div>
                    ) : tema.completado ? (
                      <FiClock className="w-4 h-4 mr-1" />
                    ) : (
                      <FiCheck className="w-4 h-4 mr-1" />
                    )}
                    {tema.completado ? 'Marcar pendiente' : 'Marcar completado'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Información sobre planificación futura */}
      {temario.tipo === 'completo' && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <div className="flex items-start">
            <FiCalendar className="w-6 h-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                Planificación Inteligente (Próximamente)
              </h3>
              <p className="text-blue-800 text-sm mb-3">
                Como tienes configurado un temario completo, pronto podrás acceder a funcionalidades avanzadas:
              </p>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Planificación automática de estudio con IA</li>
                <li>• Seguimiento de progreso personalizado</li>
                <li>• Recomendaciones de orden de estudio</li>
                <li>• Estimación de tiempos por tema</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemarioManager;
