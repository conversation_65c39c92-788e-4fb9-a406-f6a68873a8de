// Solo re-exportar el cliente del navegador para mantener compatibilidad
export { createClient, supabase } from './client';

// NOTA: Para usar el cliente del servidor, importar directamente desde './server'
// import { createServerSupabaseClient } from '@/lib/supabase/server';

// Tipos comunes
export interface Documento {
  id: string;
  titulo: string;
  contenido: string;
  categoria?: string;
  numero_tema?: number;
  creado_en: string;
  actualizado_en: string;
  user_id: string;
  tipo_original?: string;
}

export interface Conversacion {
  id: string;
  titulo: string;
  creado_en: string;
  actualizado_en: string;
  activa?: boolean;
  user_id: string;
}

export interface Mensaje {
  id: string;
  conversacion_id: string;
  tipo: 'usuario' | 'ia';
  contenido: string;
  timestamp: string;
}

export interface Flashcard {
  id: string;
  coleccion_id: string;
  pregunta: string;
  respuesta: string;
  creado_en: string;
  actualizado_en: string;
}

export interface ColeccionFlashcards {
  id: string;
  titulo: string;
  descripcion?: string;
  creado_en: string;
  actualizado_en: string;
  user_id: string;
  numero_flashcards: number; // Added to store the count of flashcards
  pendientes_hoy?: number; // Added to store the count of flashcards pending for today
}

export interface ProgresoFlashcard {
  id: string;
  flashcard_id: string;
  factor_facilidad: number;
  intervalo: number;
  repeticiones: number;
  estado: string;
  ultima_revision: string;
  proxima_revision: string;
}

export interface FlashcardConProgreso extends Flashcard {
  debeEstudiar: boolean;
  progreso?: {
    factor_facilidad: number;
    intervalo: number;
    repeticiones: number;
    estado: string;
    proxima_revision: string;
  };
}

export interface RevisionHistorial {
  id: string;
  flashcard_id: string;
  dificultad: DificultadRespuesta;
  factor_facilidad: number;
  intervalo: number;
  repeticiones: number;
  fecha: string;
}

export interface Test {
  id: string;
  titulo: string;
  descripcion?: string;
  creado_en: string;
  documentos_ids?: string[];
  user_id: string;
  numero_preguntas?: number; // Added to store the count of questions
}

export interface PreguntaTest {
  id: string;
  test_id: string;
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}

export interface EstadisticaTest {
  id: string;
  test_id: string;
  pregunta_id: string;
  respuesta_seleccionada: 'a' | 'b' | 'c' | 'd';
  es_correcta: boolean;
  fecha_respuesta: string;
  tiempo_respuesta?: number;
  created_at?: string;
}

export interface Temario {
  id: string;
  titulo: string;
  descripcion?: string;
  tipo: 'completo' | 'temas_sueltos';
  user_id: string;
  creado_en: string;
  actualizado_en: string;
}

export interface Tema {
  id: string;
  temario_id: string;
  numero: number;
  titulo: string;
  descripcion?: string;
  orden: number;
  completado: boolean;
  fecha_completado?: string;
  creado_en: string;
  actualizado_en: string;
}

export interface PlanificacionEstudio {
  id: string;
  temario_id: string;
  tema_id: string;
  fecha_planificada: string;
  tiempo_estimado?: number;
  completado: boolean;
  fecha_completado?: string;
  notas?: string;
  creado_en: string;
  actualizado_en: string;
}

export interface EstadisticasGeneralesTest {
  totalTests: number;
  totalPreguntas: number;
  totalRespuestasCorrectas: number;
  totalRespuestasIncorrectas: number;
  porcentajeAcierto: number;
}

export interface EstadisticasTestEspecifico {
  testId: string;
  totalPreguntas: number;
  totalCorrectas: number;
  totalIncorrectas: number;
  porcentajeAcierto: number;
  fechasRealizacion: string[];
  preguntasMasFalladas: {
    preguntaId: string;
    pregunta: string;
    totalFallos: number;
    totalAciertos: number;
  }[];
}

export interface EstadisticasEstudio {
  totalSesiones: number;
  totalRevisiones: number;
  distribucionDificultad: {
    dificil: number;
    normal: number;
    facil: number;
  };
  progresoTiempo: {
    fecha: string;
    nuevas: number;
    aprendiendo: number;
    repasando: number;
    aprendidas: number;
  }[];
  tarjetasMasDificiles: {
    id: string;
    pregunta: string;
    dificil: number;
    normal: number;
    facil: number;
    totalRevisiones: number;
  }[];
}

export type DificultadRespuesta = 'dificil' | 'normal' | 'facil';
export type EstadoFlashcard = 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido';
