import React, { useState, useEffect } from 'react';
import { FiBarChart2 } from 'react-icons/fi';
import {
  ColeccionFlashcards,
  obtenerColeccionesFlashcards,
  obtenerFlashcardsParaEstudiar,
  obtenerFlashcardsMasDificiles,
  obtenerFlashcardsAleatorias,
  obtenerFlashcardsNoRecientes,
  obtenerFlashcardsPorEstado,
  registrarRespuestaFlashcard,
  obtenerEstadisticasColeccion,
  eliminarColeccionFlashcards,
  eliminarFlashcard,
  FlashcardConProgreso,
  DificultadRespuesta
} from '../lib/supabase';
import FlashcardCollectionList from './flashcards/FlashcardCollectionList';
import FlashcardCollectionView from './flashcards/FlashcardCollectionView';
import FlashcardStudyOptions from './flashcards/FlashcardStudyOptions';
import FlashcardStudyMode from './flashcards/FlashcardStudyMode';
import FlashcardEditModal from './flashcards/FlashcardEditModal';
import StudyStatistics from './StudyStatistics';
import { TipoEstudio, EstadisticasColeccion } from './flashcards/types';

export default function FlashcardViewer() {
  // Estados principales
  const [colecciones, setColecciones] = useState<ColeccionFlashcards[]>([]);
  const [coleccionSeleccionada, setColeccionSeleccionada] = useState<ColeccionFlashcards | null>(null);
  const [flashcards, setFlashcards] = useState<FlashcardConProgreso[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingColecciones, setIsLoadingColecciones] = useState(true);
  const [error, setError] = useState('');
  const [activeIndex, setActiveIndex] = useState(0);
  const [estadisticas, setEstadisticas] = useState<EstadisticasColeccion | null>(null);
  const [respondiendo, setRespondiendo] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Estados para modales y modos
  const [modoEstudio, setModoEstudio] = useState(false);
  const [mostrarOpcionesEstudio, setMostrarOpcionesEstudio] = useState(false);
  const [mostrarEstadisticas, setMostrarEstadisticas] = useState(false);
  const [mostrarEditarFlashcard, setMostrarEditarFlashcard] = useState(false);
  const [flashcardParaEditar, setFlashcardParaEditar] = useState<FlashcardConProgreso | null>(null);
  const [deletingFlashcardId, setDeletingFlashcardId] = useState<string | null>(null);

  // Cargar colecciones al montar el componente
  useEffect(() => {
    const cargarColecciones = async () => {
      setIsLoadingColecciones(true);
      try {
        const data = await obtenerColeccionesFlashcards();
        setColecciones(data);
      } catch (error) {
        console.error('Error al cargar colecciones:', error);
        setError('No se pudieron cargar las colecciones de flashcards');
      } finally {
        setIsLoadingColecciones(false);
      }
    };

    cargarColecciones();
  }, []);

  const handleSeleccionarColeccion = async (coleccion: ColeccionFlashcards) => {
    setIsLoading(true);
    setError('');
    setColeccionSeleccionada(coleccion);
    setActiveIndex(0);
    setModoEstudio(false);

    try {
      // Cargar flashcards con su progreso
      const data = await obtenerFlashcardsParaEstudiar(coleccion.id) as FlashcardConProgreso[];

      // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto
      const ordenadas = [...data].sort((a, b) => {
        if (a.debeEstudiar && !b.debeEstudiar) return -1;
        if (!a.debeEstudiar && b.debeEstudiar) return 1;
        return 0;
      });

      setFlashcards(ordenadas);

      // Cargar estadísticas
      const stats = await obtenerEstadisticasColeccion(coleccion.id);
      setEstadisticas(stats);
    } catch (error) {
      console.error('Error al cargar flashcards:', error);
      setError('No se pudieron cargar las flashcards de esta colección');
    } finally {
      setIsLoading(false);
    }
  };

  const iniciarModoEstudio = async (tipoEstudio: TipoEstudio = 'programadas') => {
    setIsLoading(true);

    try {
      if (coleccionSeleccionada) {
        let flashcardsParaEstudiar: FlashcardConProgreso[] = [];

        // Obtener flashcards según el tipo de estudio seleccionado
        switch (tipoEstudio) {
          case 'programadas':
            const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
            flashcardsParaEstudiar = data.filter(flashcard => flashcard.debeEstudiar);
            break;
          case 'dificiles':
            flashcardsParaEstudiar = await obtenerFlashcardsMasDificiles(coleccionSeleccionada.id, 20);
            break;
          case 'aleatorias':
            flashcardsParaEstudiar = await obtenerFlashcardsAleatorias(coleccionSeleccionada.id, 20);
            break;
          case 'no-recientes':
            flashcardsParaEstudiar = await obtenerFlashcardsNoRecientes(coleccionSeleccionada.id, 20);
            break;
          case 'nuevas':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'nuevo', 20);
            break;
          case 'aprendiendo':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'aprendiendo', 20);
            break;
          case 'repasando':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'repasando', 20);
            break;
          case 'aprendidas':
            flashcardsParaEstudiar = await obtenerFlashcardsPorEstado(coleccionSeleccionada.id, 'aprendido', 20);
            break;
          default:
            const defaultData = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
            flashcardsParaEstudiar = defaultData.filter(flashcard => flashcard.debeEstudiar);
        }

        // Actualizar estadísticas
        const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
        setEstadisticas(stats);

        // Si no hay flashcards para el tipo seleccionado, mostrar mensaje
        if (flashcardsParaEstudiar.length === 0) {
          if (tipoEstudio === 'programadas') {
            alert('No hay flashcards programadas para estudiar hoy. Puedes usar "Opciones de estudio" para elegir otro tipo de repaso.');
            return;
          } else {
            alert(`No hay flashcards disponibles para el tipo de estudio seleccionado.`);
            return;
          }
        }

        // Usar las flashcards obtenidas
        setFlashcards(flashcardsParaEstudiar);

        // Iniciar el modo de estudio
        setModoEstudio(true);
        setActiveIndex(0);
        setMostrarOpcionesEstudio(false);
      }
    } catch (error) {
      console.error('Error al iniciar modo estudio:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const salirModoEstudio = async () => {
    setModoEstudio(false);

    // Recargar las flashcards y estadísticas
    if (coleccionSeleccionada) {
      try {
        const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
        const ordenadas = [...data].sort((a, b) => {
          if (a.debeEstudiar && !b.debeEstudiar) return -1;
          if (!a.debeEstudiar && b.debeEstudiar) return 1;
          return 0;
        });
        setFlashcards(ordenadas);

        const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
        setEstadisticas(stats);
      } catch (error) {
        console.error('Error al recargar datos:', error);
      }
    }
  };

  const handleRespuesta = async (dificultad: DificultadRespuesta) => {
    if (!flashcards[activeIndex]) return;

    setRespondiendo(true);

    try {
      // Registrar la respuesta
      const exito = await registrarRespuestaFlashcard(flashcards[activeIndex].id, dificultad);

      if (!exito) {
        throw new Error('Error al registrar la respuesta');
      }

      // Si es la última tarjeta, terminar la sesión
      if (activeIndex >= flashcards.length - 1) {
        alert('¡Has completado la sesión de estudio!');
        await salirModoEstudio();
      } else {
        // Avanzar a la siguiente tarjeta
        setActiveIndex(activeIndex + 1);
      }
    } catch (error) {
      console.error('Error al actualizar progreso:', error);
      setError('Error al guardar tu respuesta. Por favor, inténtalo de nuevo.');
    } finally {
      setRespondiendo(false);
    }
  };

  const handleNavigate = (direction: 'prev' | 'next') => {
    if (direction === 'next' && activeIndex < flashcards.length - 1) {
      setActiveIndex(activeIndex + 1);
    } else if (direction === 'prev' && activeIndex > 0) {
      setActiveIndex(activeIndex - 1);
    }
  };

  const handleEliminarColeccion = async (coleccionId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta colección? Esta acción no se puede deshacer.')) {
      return;
    }

    setDeletingId(coleccionId);
    try {
      const exito = await eliminarColeccionFlashcards(coleccionId);
      if (exito) {
        // Recargar las colecciones
        const data = await obtenerColeccionesFlashcards();
        setColecciones(data);

        // Si la colección eliminada era la seleccionada, deseleccionarla
        if (coleccionSeleccionada?.id === coleccionId) {
          setColeccionSeleccionada(null);
          setFlashcards([]);
          setEstadisticas(null);
        }
      } else {
        setError('No se pudo eliminar la colección');
      }
    } catch (error) {
      console.error('Error al eliminar colección:', error);
      setError('Error al eliminar la colección');
    } finally {
      setDeletingId(null);
    }
  };

  const handleEditarFlashcard = (flashcard: FlashcardConProgreso) => {
    setFlashcardParaEditar(flashcard);
    setMostrarEditarFlashcard(true);
  };

  const handleEliminarFlashcard = async (flashcardId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard? Esta acción no se puede deshacer.')) {
      return;
    }

    setDeletingFlashcardId(flashcardId);
    try {
      const exito = await eliminarFlashcard(flashcardId);
      if (exito) {
        // Recargar las flashcards de la colección actual
        if (coleccionSeleccionada) {
          const data = await obtenerFlashcardsParaEstudiar(coleccionSeleccionada.id);
          const ordenadas = [...data].sort((a, b) => {
            if (a.debeEstudiar && !b.debeEstudiar) return -1;
            if (!a.debeEstudiar && b.debeEstudiar) return 1;
            return 0;
          });
          setFlashcards(ordenadas);

          // Actualizar estadísticas
          const stats = await obtenerEstadisticasColeccion(coleccionSeleccionada.id);
          setEstadisticas(stats);
        }
      } else {
        setError('No se pudo eliminar la flashcard');
      }
    } catch (error) {
      console.error('Error al eliminar flashcard:', error);
      setError('Error al eliminar la flashcard');
    } finally {
      setDeletingFlashcardId(null);
    }
  };

  const handleGuardarFlashcard = async (flashcardActualizada: FlashcardConProgreso) => {
    // Actualizar la flashcard en el estado local
    setFlashcards(prevFlashcards =>
      prevFlashcards.map(fc =>
        fc.id === flashcardActualizada.id ? flashcardActualizada : fc
      )
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {modoEstudio ? (
        <FlashcardStudyMode
          flashcards={flashcards}
          activeIndex={activeIndex}
          respondiendo={respondiendo}
          onRespuesta={handleRespuesta}
          onNavigate={handleNavigate}
          onVolver={salirModoEstudio}
        />
      ) : (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Mis Flashcards</h2>
            <button
              onClick={() => {/* TODO: Implementar estadísticas generales */}}
              className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center"
            >
              <FiBarChart2 className="mr-2" /> Estadísticas Generales
            </button>
          </div>

          {!coleccionSeleccionada ? (
            <FlashcardCollectionList
              colecciones={colecciones}
              coleccionSeleccionada={coleccionSeleccionada}
              onSeleccionarColeccion={handleSeleccionarColeccion}
              onEliminarColeccion={handleEliminarColeccion}
              isLoading={isLoadingColecciones}
              deletingId={deletingId}
            />
          ) : (
            <div>
              {/* Botón para volver a la lista */}
              <button
                onClick={() => setColeccionSeleccionada(null)}
                className="mb-4 text-blue-600 hover:text-blue-800 flex items-center"
              >
                ← Volver a mis colecciones
              </button>

              <FlashcardCollectionView
                coleccion={coleccionSeleccionada}
                flashcards={flashcards}
                estadisticas={estadisticas}
                isLoading={isLoading}
                onStartStudy={() => iniciarModoEstudio('programadas')}
                onShowStudyOptions={() => setMostrarOpcionesEstudio(true)}
                onShowStatistics={() => setMostrarEstadisticas(true)}
                onEditFlashcard={handleEditarFlashcard}
                onDeleteFlashcard={handleEliminarFlashcard}
                deletingFlashcardId={deletingFlashcardId}
              />
            </div>
          )}
        </div>
      )}

      {/* Modal de opciones de estudio */}
      <FlashcardStudyOptions
        isOpen={mostrarOpcionesEstudio}
        onClose={() => setMostrarOpcionesEstudio(false)}
        onSelectStudyType={iniciarModoEstudio}
        estadisticas={estadisticas}
        isLoading={isLoading}
      />

      {/* Modal de estadísticas detalladas */}
      {mostrarEstadisticas && coleccionSeleccionada && (
        <StudyStatistics
          coleccionId={coleccionSeleccionada.id}
          onClose={() => setMostrarEstadisticas(false)}
        />
      )}

      {/* Modal de edición de flashcard */}
      {flashcardParaEditar && (
        <FlashcardEditModal
          flashcard={flashcardParaEditar}
          isOpen={mostrarEditarFlashcard}
          onClose={() => {
            setMostrarEditarFlashcard(false);
            setFlashcardParaEditar(null);
          }}
          onSave={handleGuardarFlashcard}
        />
      )}
    </div>
  );
}
