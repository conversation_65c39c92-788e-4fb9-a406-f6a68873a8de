"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx":
/*!*************************************************************!*\
  !*** ./src/components/flashcards/FlashcardStudyOptions.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiBookOpen,FiClock,FiHelpCircle,FiLoader,FiRefreshCw,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst FlashcardStudyOptions = (param)=>{\n    let { isOpen, onClose, onSelectStudyType, estadisticas, isLoading = false } = param;\n    const opcionesEstudio = [\n        {\n            tipo: 'dificiles',\n            label: 'Más difíciles',\n            descripcion: 'Tarjetas que has marcado como difíciles más frecuentemente',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiHelpCircle, {\n                className: \"text-red-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 25,\n                columnNumber: 13\n            }, undefined),\n            color: 'red-600',\n            bgColor: 'bg-red-100',\n            hoverBgColor: 'hover:bg-red-200'\n        },\n        {\n            tipo: 'aleatorias',\n            label: 'Aleatorias',\n            descripcion: 'Selección aleatoria de tarjetas de la colección',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                className: \"text-purple-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, undefined),\n            color: 'purple-600',\n            bgColor: 'bg-purple-100',\n            hoverBgColor: 'hover:bg-purple-200'\n        },\n        {\n            tipo: 'no-recientes',\n            label: 'No estudiadas recientemente',\n            descripcion: 'Tarjetas que no has revisado en mucho tiempo',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                className: \"text-orange-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, undefined),\n            color: 'orange-600',\n            bgColor: 'bg-orange-100',\n            hoverBgColor: 'hover:bg-orange-200'\n        },\n        {\n            tipo: 'nuevas',\n            label: 'Nuevas',\n            descripcion: 'Tarjetas que nunca has estudiado',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiBookOpen, {\n                className: \"text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, undefined),\n            color: 'green-600',\n            bgColor: 'bg-green-100',\n            hoverBgColor: 'hover:bg-green-200'\n        },\n        {\n            tipo: 'aprendiendo',\n            label: 'En aprendizaje',\n            descripcion: 'Tarjetas que estás aprendiendo actualmente',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiLoader, {\n                className: \"text-yellow-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, undefined),\n            color: 'yellow-600',\n            bgColor: 'bg-yellow-100',\n            hoverBgColor: 'hover:bg-yellow-200'\n        }\n    ];\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"Opciones de Estudio\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBookOpen_FiClock_FiHelpCircle_FiLoader_FiRefreshCw_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-3\",\n                            children: \"Elige el tipo de estudio que prefieras. Cada opci\\xf3n te permitir\\xe1 enfocar tu aprendizaje de manera diferente:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800 font-medium\",\n                                children: [\n                                    \"ℹ️ Importante: Estos estudios adicionales son complementarios y \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"no afectan al algoritmo de repetici\\xf3n espaciada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 79\n                                    }, undefined),\n                                    '. Para el estudio oficial que cuenta para tu progreso, usa el bot\\xf3n \"Estudiar\" principal.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: opcionesEstudio.map((opcion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onSelectStudyType(opcion.tipo),\n                            className: \"p-4 border rounded-lg text-left transition-all duration-200 \".concat(opcion.hoverBgColor, \" \").concat(opcion.bgColor, \" border-gray-200 hover:border-gray-300\"),\n                            disabled: isLoading,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-1\",\n                                        children: opcion.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-\".concat(opcion.color, \" mb-1\"),\n                                                children: opcion.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: opcion.descripcion\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined)\n                        }, opcion.tipo, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex justify-end space-x-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                        children: \"Cancelar\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardStudyOptions.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_c = FlashcardStudyOptions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardStudyOptions);\nvar _c;\n$RefreshReg$(_c, \"FlashcardStudyOptions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardStudyOptions.tsx\n"));

/***/ })

});