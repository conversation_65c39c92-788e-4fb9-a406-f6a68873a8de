import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

interface UseAutoLogoutOptions {
  timeoutMinutes?: number;
  warningMinutes?: number;
  enabled?: boolean;
}

export const useAutoLogout = (options: UseAutoLogoutOptions = {}) => {
  const {
    timeoutMinutes = 5,
    warningMinutes = 0.5, // 30 segundos antes
    enabled = true
  } = options;

  const { user, logout } = useAuth();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const warningShownRef = useRef<boolean>(false);
  const pageVisibleRef = useRef<boolean>(true);

  // Convertir minutos a milisegundos
  const timeoutMs = timeoutMinutes * 60 * 1000;
  const warningMs = warningMinutes * 60 * 1000;

  const clearTimeouts = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
      warningTimeoutRef.current = null;
    }
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      // Limpiar timeouts antes del logout
      clearTimeouts();

      await logout();
      toast.error('Sesión cerrada por inactividad', {
        duration: 4000,
        position: 'top-center',
        id: 'auto-logout' // Evitar duplicados
      });
    } catch (error) {
      console.error('Error al cerrar sesión por inactividad:', error);
      // Intentar logout forzado si hay error
      try {
        window.location.href = '/login';
      } catch (redirectError) {
        console.error('Error al redirigir:', redirectError);
      }
    }
  }, [logout, clearTimeouts]);

  const showWarning = useCallback(() => {
    if (!warningShownRef.current) {
      warningShownRef.current = true;
      toast.warning('⚠️ Tu sesión se cerrará en 30 segundos por inactividad', {
        duration: 4000,
        position: 'top-center',
        id: 'auto-logout-warning', // Evitar duplicados
        style: {
          background: '#f59e0b',
          color: '#fff',
          fontWeight: 'bold'
        }
      });
    }
  }, []);

  const resetTimer = useCallback(() => {
    if (!enabled || !user) return;

    lastActivityRef.current = Date.now();
    warningShownRef.current = false;
    clearTimeouts();

    // Configurar timeout para mostrar advertencia
    if (warningMs > 0) {
      warningTimeoutRef.current = setTimeout(() => {
        showWarning();
      }, timeoutMs - warningMs);
    }

    // Configurar timeout para logout
    timeoutRef.current = setTimeout(() => {
      handleLogout();
    }, timeoutMs);
  }, [enabled, user, timeoutMs, warningMs, clearTimeouts, handleLogout, showWarning]);

  // Eventos que indican actividad del usuario
  const activityEvents = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ];

  const handleActivity = useCallback((event: Event) => {
    // Evitar resetear el timer por movimientos mínimos del mouse
    if (event.type === 'mousemove') {
      const now = Date.now();
      if (now - lastActivityRef.current < 1000) { // Throttle de 1 segundo
        return;
      }
    }
    resetTimer();
  }, [resetTimer]);

  useEffect(() => {
    if (!enabled || !user) {
      clearTimeouts();
      return;
    }

    // Inicializar timer
    resetTimer();

    // Agregar event listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Cleanup
    return () => {
      clearTimeouts();
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [enabled, user, resetTimer, handleActivity, clearTimeouts]);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      clearTimeouts();
    };
  }, [clearTimeouts]);

  return {
    resetTimer,
    clearTimeouts,
    isEnabled: enabled && !!user
  };
};

export default useAutoLogout;
