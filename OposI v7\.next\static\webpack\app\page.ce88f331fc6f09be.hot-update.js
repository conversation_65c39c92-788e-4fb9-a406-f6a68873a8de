"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarConversacion),\n/* harmony export */   actualizarFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearColeccionFlashcards),\n/* harmony export */   crearConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearConversacion),\n/* harmony export */   crearFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearFlashcard),\n/* harmony export */   crearPreguntaTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.crearTest),\n/* harmony export */   createClient: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.desactivarTodasLasConversaciones),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarColeccionFlashcards),\n/* harmony export */   eliminarConversacion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarConversacion),\n/* harmony export */   eliminarDocumento: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarDocumento),\n/* harmony export */   eliminarFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.eliminarFlashcard),\n/* harmony export */   guardarDocumento: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarDocumento),\n/* harmony export */   guardarFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarFlashcards),\n/* harmony export */   guardarMensaje: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarMensaje),\n/* harmony export */   guardarPreguntasTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarPreguntasTest),\n/* harmony export */   guardarRevisionHistorial: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerColeccionesFlashcards),\n/* harmony export */   obtenerConversacionActiva: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerConversaciones),\n/* harmony export */   obtenerDocumentoPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerDocumentos),\n/* harmony export */   obtenerEstadisticasColeccion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasColeccion),\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasDashboard),\n/* harmony export */   obtenerEstadisticasDetalladas: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasDetalladas),\n/* harmony export */   obtenerEstadisticasEstudio: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasEstudio),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerEstadisticasTest),\n/* harmony export */   obtenerFlashcardPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerHistorialRevisiones: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerHistorialRevisiones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerMensajesPorConversacionId),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerPreguntasTestCount),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerProgresoFlashcard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerProximasFlashcards),\n/* harmony export */   obtenerTestPorId: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.obtenerTests),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.registrarRespuestaFlashcard),\n/* harmony export */   registrarRespuestaTest: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.registrarRespuestaTest),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.reiniciarProgresoFlashcard),\n/* harmony export */   supabase: () => (/* reexport safe */ _supabase_index__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase/index */ \"(app-pages-browser)/./src/lib/supabase/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsOEZBQThGO0FBQzlGLGlFQUFpRTtBQUVoQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcT3Bvc0kgdjdcXHNyY1xcbGliXFxzdXBhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFc3RlIGFyY2hpdm8gZXMgdW4gcHVudG8gZGUgZW50cmFkYSBwYXJhIG1hbnRlbmVyIGxhIGNvbXBhdGliaWxpZGFkIGNvbiBlbCBjw7NkaWdvIGV4aXN0ZW50ZVxuLy8gUmVkaXJpZ2UgdG9kYXMgbGFzIGV4cG9ydGFjaW9uZXMgYSBsYSBudWV2YSBlc3RydWN0dXJhIG1vZHVsYXJcblxuZXhwb3J0ICogZnJvbSBcIi4vc3VwYWJhc2UvaW5kZXhcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/flashcardsService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/flashcardsService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actualizarFlashcard: () => (/* binding */ actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* binding */ actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* binding */ crearColeccionFlashcards),\n/* harmony export */   crearFlashcard: () => (/* binding */ crearFlashcard),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* binding */ eliminarColeccionFlashcards),\n/* harmony export */   eliminarFlashcard: () => (/* binding */ eliminarFlashcard),\n/* harmony export */   guardarFlashcards: () => (/* binding */ guardarFlashcards),\n/* harmony export */   guardarRevisionHistorial: () => (/* binding */ guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* binding */ obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* binding */ obtenerColeccionesFlashcards),\n/* harmony export */   obtenerFlashcardPorId: () => (/* binding */ obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* binding */ obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* binding */ obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* binding */ obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* binding */ obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* binding */ obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* binding */ obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* binding */ obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* binding */ obtenerProgresoFlashcard),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* binding */ registrarRespuestaFlashcard),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* binding */ reiniciarProgresoFlashcard)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Crea una nueva colección de flashcards\n */ async function crearColeccionFlashcards(titulo, descripcion) {\n    try {\n        var _data_;\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').insert([\n            {\n                titulo,\n                descripcion,\n                user_id: user.id\n            }\n        ]).select();\n        if (error) {\n            console.error('Error al crear colección de flashcards:', error);\n            return null;\n        }\n        return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n    } catch (error) {\n        console.error('Error al crear colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Obtiene todas las colecciones de flashcards del usuario actual\n */ async function obtenerColeccionesFlashcards() {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return [];\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards\n        .eq('user_id', user.id).order('creado_en', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener colecciones de flashcards:', error);\n            return [];\n        }\n        // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]\n        // We need to transform this to a direct property numero_flashcards\n        return (data === null || data === void 0 ? void 0 : data.map((coleccion)=>({\n                ...coleccion,\n                // @ts-ignore Supabase types might not be perfect here for related counts\n                numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0 ? coleccion.flashcards[0].count : 0\n            }))) || [];\n    } catch (error) {\n        console.error('Error al obtener colecciones de flashcards:', error);\n        return [];\n    }\n}\n/**\n * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)\n */ async function obtenerColeccionFlashcardsPorId(id) {\n    try {\n        // Obtener el usuario actual\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').select('*').eq('id', id).eq('user_id', user.id).single();\n        if (error) {\n            console.error('Error al obtener colección de flashcards:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error al obtener colección de flashcards:', error);\n        return null;\n    }\n}\n/**\n * Crea una nueva flashcard\n */ async function crearFlashcard(coleccionId, pregunta, respuesta) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert([\n        {\n            coleccion_id: coleccionId,\n            pregunta,\n            respuesta\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al crear flashcard:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Obtiene todas las flashcards de una colección\n */ async function obtenerFlashcardsPorColeccionId(coleccionId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('coleccion_id', coleccionId).order('creado_en', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error al obtener flashcards:', error);\n        return [];\n    }\n    return data || [];\n}\n// Alias para mantener compatibilidad con el código existente\nconst obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;\n/**\n * Guarda múltiples flashcards en la base de datos\n */ async function guardarFlashcards(flashcards) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').insert(flashcards).select();\n    if (error) {\n        console.error('Error al guardar flashcards:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((card)=>card.id)) || null;\n}\n/**\n * Obtiene una flashcard por su ID\n */ async function obtenerFlashcardPorId(id) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('*').eq('id', id).single();\n    if (error) {\n        console.error('Error al obtener flashcard:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene el progreso de una flashcard\n */ async function obtenerProgresoFlashcard(flashcardId) {\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').eq('flashcard_id', flashcardId).maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro\n    if (error) {\n        console.error('Error al obtener progreso de flashcard:', error);\n        return null;\n    }\n    return data || null;\n}\n/**\n * Obtiene todas las flashcards con su progreso para una colección\n */ async function obtenerFlashcardsParaEstudiar(coleccionId) {\n    // Obtener todas las flashcards de la colección\n    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);\n    // Obtener el progreso de todas las flashcards en una sola consulta\n    const { data: progresos, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('*').in('flashcard_id', flashcards.map((f)=>f.id));\n    if (error) {\n        console.error('Error al obtener progreso de flashcards:', error);\n        return [];\n    }\n    // Fecha actual para comparar\n    const ahora = new Date();\n    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());\n    // Combinar flashcards con su progreso\n    return flashcards.map((flashcard)=>{\n        const progreso = progresos === null || progresos === void 0 ? void 0 : progresos.find((p)=>p.flashcard_id === flashcard.id);\n        if (!progreso) {\n            // Si no hay progreso, es una tarjeta nueva que debe estudiarse\n            return {\n                ...flashcard,\n                debeEstudiar: true\n            };\n        }\n        // Determinar si la flashcard debe estudiarse hoy\n        const proximaRevision = new Date(progreso.proxima_revision);\n        const proximaRevisionSinHora = new Date(proximaRevision.getFullYear(), proximaRevision.getMonth(), proximaRevision.getDate());\n        const debeEstudiar = proximaRevisionSinHora <= hoy;\n        return {\n            ...flashcard,\n            debeEstudiar,\n            progreso: {\n                factor_facilidad: progreso.factor_facilidad,\n                intervalo: progreso.intervalo,\n                repeticiones: progreso.repeticiones,\n                estado: progreso.estado,\n                proxima_revision: progreso.proxima_revision\n            }\n        };\n    });\n}\n/**\n * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)\n */ async function obtenerFlashcardsMasDificiles(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        // Obtener todas las flashcards con progreso\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener historial de revisiones para calcular dificultad\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: historial, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('revision_historial').select('flashcard_id, dificultad').in('flashcard_id', flashcardIds);\n        if (error) {\n            console.error('Error al obtener historial de revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Calcular estadísticas de dificultad por flashcard\n        const estadisticasDificultad = new Map();\n        historial === null || historial === void 0 ? void 0 : historial.forEach((revision)=>{\n            const stats = estadisticasDificultad.get(revision.flashcard_id) || {\n                dificil: 0,\n                total: 0\n            };\n            stats.total++;\n            if (revision.dificultad === 'dificil') {\n                stats.dificil++;\n            }\n            estadisticasDificultad.set(revision.flashcard_id, stats);\n        });\n        // Ordenar por dificultad (ratio de respuestas difíciles)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const stats = estadisticasDificultad.get(flashcard.id);\n            const ratioDificultad = stats ? stats.dificil / stats.total : 0;\n            return {\n                ...flashcard,\n                ratioDificultad\n            };\n        }).sort((a, b)=>b.ratioDificultad - a.ratioDificultad).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards más difíciles:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards aleatorias de una colección\n */ async function obtenerFlashcardsAleatorias(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Mezclar aleatoriamente y tomar el límite\n        const flashcardsMezcladas = [\n            ...flashcardsConProgreso\n        ].sort(()=>Math.random() - 0.5).slice(0, limite);\n        return flashcardsMezcladas;\n    } catch (error) {\n        console.error('Error al obtener flashcards aleatorias:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards que no se han estudiado recientemente\n */ async function obtenerFlashcardsNoRecientes(coleccionId) {\n    let limite = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Obtener última revisión de cada flashcard\n        const flashcardIds = flashcardsConProgreso.map((f)=>f.id);\n        const { data: ultimasRevisiones, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('revision_historial').select('flashcard_id, fecha_revision').in('flashcard_id', flashcardIds).order('fecha_revision', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener últimas revisiones:', error);\n            return flashcardsConProgreso.slice(0, limite);\n        }\n        // Obtener la fecha más reciente por flashcard\n        const ultimaRevisionPorFlashcard = new Map();\n        ultimasRevisiones === null || ultimasRevisiones === void 0 ? void 0 : ultimasRevisiones.forEach((revision)=>{\n            if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {\n                ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha_revision);\n            }\n        });\n        // Ordenar por fecha de última revisión (más antiguas primero)\n        const flashcardsOrdenadas = flashcardsConProgreso.map((flashcard)=>{\n            const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);\n            return {\n                ...flashcard,\n                ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)\n            };\n        }).sort((a, b)=>a.ultimaRevision.getTime() - b.ultimaRevision.getTime()).slice(0, limite);\n        return flashcardsOrdenadas;\n    } catch (error) {\n        console.error('Error al obtener flashcards no recientes:', error);\n        return [];\n    }\n}\n/**\n * Obtiene flashcards por estado específico\n */ async function obtenerFlashcardsPorEstado(coleccionId, estado) {\n    let limite = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 10;\n    try {\n        const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);\n        // Filtrar por estado y limitar\n        const flashcardsFiltradas = flashcardsConProgreso.filter((flashcard)=>{\n            if (!flashcard.progreso) {\n                return estado === 'nuevo';\n            }\n            return flashcard.progreso.estado === estado;\n        }).slice(0, limite);\n        return flashcardsFiltradas;\n    } catch (error) {\n        console.error('Error al obtener flashcards por estado:', error);\n        return [];\n    }\n}\n/**\n * Registra una respuesta a una flashcard y actualiza su progreso\n * Versión simplificada para evitar errores 406\n */ async function registrarRespuestaFlashcard(flashcardId, dificultad) {\n    try {\n        // Primero intentamos obtener el progreso existente\n        let factorFacilidad = 2.5;\n        let intervalo = 1;\n        let repeticiones = 0;\n        let estado = 'nuevo';\n        // Intentar obtener progreso existente\n        try {\n            const { data: progresoExistente } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('factor_facilidad, intervalo, repeticiones, estado').eq('flashcard_id', flashcardId).limit(1);\n            if (progresoExistente && progresoExistente.length > 0) {\n                const progreso = progresoExistente[0];\n                factorFacilidad = progreso.factor_facilidad || 2.5;\n                intervalo = progreso.intervalo || 1;\n                repeticiones = progreso.repeticiones || 0;\n                estado = progreso.estado || 'nuevo';\n            }\n        } catch (error) {\n        // Usar valores por defecto si hay error\n        }\n        // Aplicar el algoritmo SM-2 para calcular el nuevo progreso\n        let nuevoFactorFacilidad = factorFacilidad;\n        let nuevoIntervalo = intervalo;\n        let nuevasRepeticiones = repeticiones;\n        let nuevoEstado = estado;\n        // Ajustar el factor de facilidad según la dificultad reportada\n        if (dificultad === 'dificil') {\n            nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);\n            nuevasRepeticiones = 0;\n            nuevoIntervalo = 1;\n            nuevoEstado = 'aprendiendo';\n        } else {\n            nuevasRepeticiones++;\n            if (dificultad === 'normal') {\n                nuevoFactorFacilidad = factorFacilidad - 0.15;\n            } else if (dificultad === 'facil') {\n                nuevoFactorFacilidad = factorFacilidad + 0.1;\n            }\n            nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));\n            // Calcular el nuevo intervalo\n            if (nuevasRepeticiones === 1) {\n                nuevoIntervalo = 1;\n                nuevoEstado = 'aprendiendo';\n            } else if (nuevasRepeticiones === 2) {\n                nuevoIntervalo = 6;\n                nuevoEstado = 'repasando';\n            } else {\n                nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);\n                nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';\n            }\n        }\n        // Calcular la próxima fecha de revisión\n        const ahora = new Date();\n        const proximaRevision = new Date(ahora);\n        proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);\n        // Guardar el nuevo progreso\n        const { error: errorUpsert } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').upsert({\n            flashcard_id: flashcardId,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            estado: nuevoEstado,\n            ultima_revision: ahora.toISOString(),\n            proxima_revision: proximaRevision.toISOString()\n        });\n        if (errorUpsert) {\n            return false;\n        }\n        // Guardar en el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert({\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: nuevoFactorFacilidad,\n            intervalo: nuevoIntervalo,\n            repeticiones: nuevasRepeticiones,\n            fecha: ahora.toISOString()\n        });\n        if (errorHistorial) {\n        // No retornamos false aquí porque el progreso ya se guardó correctamente\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n// Alias para mantener compatibilidad con el código existente\nconst actualizarProgresoFlashcard = registrarRespuestaFlashcard;\n/**\n * Guarda una revisión en el historial\n */ async function guardarRevisionHistorial(flashcardId, dificultad, factorFacilidad, intervalo, repeticiones) {\n    var _data_;\n    const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').insert([\n        {\n            flashcard_id: flashcardId,\n            dificultad,\n            factor_facilidad: factorFacilidad,\n            intervalo,\n            repeticiones\n        }\n    ]).select();\n    if (error) {\n        console.error('Error al guardar revisión en historial:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : (_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id) || null;\n}\n/**\n * Reinicia el progreso de una flashcard\n */ async function reiniciarProgresoFlashcard(flashcardId) {\n    const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').update({\n        factor_facilidad: 2.5,\n        intervalo: 0,\n        repeticiones: 0,\n        estado: 'nuevo',\n        ultima_revision: new Date().toISOString(),\n        proxima_revision: new Date().toISOString()\n    }).eq('flashcard_id', flashcardId);\n    if (error) {\n        console.error('Error al reiniciar progreso de flashcard:', error);\n        return false;\n    }\n    return true;\n}\n/**\n * Actualiza una flashcard existente\n */ async function actualizarFlashcard(flashcardId, pregunta, respuesta) {\n    try {\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').update({\n            pregunta,\n            respuesta,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', flashcardId);\n        if (error) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una flashcard y todo su progreso asociado\n */ async function eliminarFlashcard(flashcardId) {\n    try {\n        // Primero eliminar el progreso asociado\n        const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().eq('flashcard_id', flashcardId);\n        if (errorProgreso) {\n            return false;\n        }\n        // Eliminar el historial de revisiones\n        const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().eq('flashcard_id', flashcardId);\n        if (errorHistorial) {\n            return false;\n        }\n        // Finalmente eliminar la flashcard\n        const { error: errorFlashcard, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete({\n            count: 'exact'\n        }).eq('id', flashcardId);\n        if (errorFlashcard) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n/**\n * Elimina una colección completa de flashcards y todo su contenido asociado\n */ async function eliminarColeccionFlashcards(coleccionId) {\n    try {\n        // Obtener el usuario actual para verificar permisos\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return false;\n        }\n        // Primero obtener todas las flashcards de la colección\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id').eq('coleccion_id', coleccionId);\n        if (errorFlashcards) {\n            return false;\n        }\n        const flashcardIds = (flashcards === null || flashcards === void 0 ? void 0 : flashcards.map((fc)=>fc.id)) || [];\n        // Eliminar progreso de todas las flashcards\n        if (flashcardIds.length > 0) {\n            const { error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').delete().in('flashcard_id', flashcardIds);\n            if (errorProgreso) {\n                return false;\n            }\n            // Eliminar historial de todas las flashcards\n            const { error: errorHistorial } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('historial_revisiones').delete().in('flashcard_id', flashcardIds);\n            if (errorHistorial) {\n                return false;\n            }\n            // Eliminar todas las flashcards de la colección\n            const { error: errorFlashcardsDelete } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').delete().eq('coleccion_id', coleccionId);\n            if (errorFlashcardsDelete) {\n                return false;\n            }\n        }\n        // Finalmente eliminar la colección\n        const { error: errorColeccion, count } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('colecciones_flashcards').delete({\n            count: 'exact'\n        }).eq('id', coleccionId).eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual\n        if (errorColeccion) {\n            return false;\n        }\n        if (count === 0) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase/index.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/index.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.activarConversacion),\n/* harmony export */   actualizarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.actualizarConversacion),\n/* harmony export */   actualizarFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.actualizarFlashcard),\n/* harmony export */   actualizarProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.actualizarProgresoFlashcard),\n/* harmony export */   crearColeccionFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.crearColeccionFlashcards),\n/* harmony export */   crearConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.crearConversacion),\n/* harmony export */   crearFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.crearFlashcard),\n/* harmony export */   crearPreguntaTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.crearPreguntaTest),\n/* harmony export */   crearTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.crearTest),\n/* harmony export */   createClient: () => (/* reexport safe */ _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   desactivarTodasLasConversaciones: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.desactivarTodasLasConversaciones),\n/* harmony export */   eliminarColeccionFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.eliminarColeccionFlashcards),\n/* harmony export */   eliminarConversacion: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.eliminarConversacion),\n/* harmony export */   eliminarDocumento: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.eliminarDocumento),\n/* harmony export */   eliminarFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.eliminarFlashcard),\n/* harmony export */   guardarDocumento: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.guardarDocumento),\n/* harmony export */   guardarFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.guardarFlashcards),\n/* harmony export */   guardarMensaje: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.guardarMensaje),\n/* harmony export */   guardarPreguntasTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.guardarPreguntasTest),\n/* harmony export */   guardarRevisionHistorial: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.guardarRevisionHistorial),\n/* harmony export */   obtenerColeccionFlashcardsPorId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerColeccionFlashcardsPorId),\n/* harmony export */   obtenerColeccionesFlashcards: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerColeccionesFlashcards),\n/* harmony export */   obtenerConversacionActiva: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionActiva),\n/* harmony export */   obtenerConversacionPorId: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversacionPorId),\n/* harmony export */   obtenerConversaciones: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerConversaciones),\n/* harmony export */   obtenerDocumentoPorId: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.obtenerDocumentoPorId),\n/* harmony export */   obtenerDocumentos: () => (/* reexport safe */ _documentosService__WEBPACK_IMPORTED_MODULE_1__.obtenerDocumentos),\n/* harmony export */   obtenerEstadisticasColeccion: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion),\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* reexport safe */ _dashboardService__WEBPACK_IMPORTED_MODULE_6__.obtenerEstadisticasDashboard),\n/* harmony export */   obtenerEstadisticasDetalladas: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasDetalladas),\n/* harmony export */   obtenerEstadisticasEstudio: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasEstudio),\n/* harmony export */   obtenerEstadisticasGeneralesTests: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerEstadisticasGeneralesTests),\n/* harmony export */   obtenerEstadisticasTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerEstadisticasTest),\n/* harmony export */   obtenerFlashcardPorId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardPorId),\n/* harmony export */   obtenerFlashcardsAleatorias: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsAleatorias),\n/* harmony export */   obtenerFlashcardsMasDificiles: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsMasDificiles),\n/* harmony export */   obtenerFlashcardsNoRecientes: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsNoRecientes),\n/* harmony export */   obtenerFlashcardsParaEstudiar: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsParaEstudiar),\n/* harmony export */   obtenerFlashcardsPorColeccion: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorColeccion),\n/* harmony export */   obtenerFlashcardsPorColeccionId: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorColeccionId),\n/* harmony export */   obtenerFlashcardsPorEstado: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerFlashcardsPorEstado),\n/* harmony export */   obtenerHistorialRevisiones: () => (/* reexport safe */ _estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerHistorialRevisiones),\n/* harmony export */   obtenerMensajesPorConversacionId: () => (/* reexport safe */ _conversacionesService__WEBPACK_IMPORTED_MODULE_2__.obtenerMensajesPorConversacionId),\n/* harmony export */   obtenerPreguntasPorTestId: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerPreguntasPorTestId),\n/* harmony export */   obtenerPreguntasTestCount: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerPreguntasTestCount),\n/* harmony export */   obtenerProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.obtenerProgresoFlashcard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* reexport safe */ _dashboardService__WEBPACK_IMPORTED_MODULE_6__.obtenerProximasFlashcards),\n/* harmony export */   obtenerTestPorId: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerTestPorId),\n/* harmony export */   obtenerTests: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.obtenerTests),\n/* harmony export */   registrarRespuestaFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.registrarRespuestaFlashcard),\n/* harmony export */   registrarRespuestaTest: () => (/* reexport safe */ _testsService__WEBPACK_IMPORTED_MODULE_5__.registrarRespuestaTest),\n/* harmony export */   reiniciarProgresoFlashcard: () => (/* reexport safe */ _flashcardsService__WEBPACK_IMPORTED_MODULE_3__.reiniciarProgresoFlashcard),\n/* harmony export */   supabase: () => (/* reexport safe */ _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _documentosService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./documentosService */ \"(app-pages-browser)/./src/lib/supabase/documentosService.ts\");\n/* harmony import */ var _conversacionesService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversacionesService */ \"(app-pages-browser)/./src/lib/supabase/conversacionesService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n/* harmony import */ var _dashboardService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./dashboardService */ \"(app-pages-browser)/./src/lib/supabase/dashboardService.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsZ0RBQWdEO0FBQ2Y7QUFDRztBQUNJO0FBQ0o7QUFDRTtBQUNQO0FBQ0kiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGxpYlxcc3VwYWJhc2VcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydGFyIHRvZG8gZGVzZGUgbG9zIGFyY2hpdm9zIGluZGl2aWR1YWxlc1xuZXhwb3J0ICogZnJvbSAnLi9zdXBhYmFzZUNsaWVudCc7XG5leHBvcnQgKiBmcm9tICcuL2RvY3VtZW50b3NTZXJ2aWNlJztcbmV4cG9ydCAqIGZyb20gJy4vY29udmVyc2FjaW9uZXNTZXJ2aWNlJztcbmV4cG9ydCAqIGZyb20gJy4vZmxhc2hjYXJkc1NlcnZpY2UnO1xuZXhwb3J0ICogZnJvbSAnLi9lc3RhZGlzdGljYXNTZXJ2aWNlJztcbmV4cG9ydCAqIGZyb20gJy4vdGVzdHNTZXJ2aWNlJztcbmV4cG9ydCAqIGZyb20gJy4vZGFzaGJvYXJkU2VydmljZSc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/index.ts\n"));

/***/ })

});