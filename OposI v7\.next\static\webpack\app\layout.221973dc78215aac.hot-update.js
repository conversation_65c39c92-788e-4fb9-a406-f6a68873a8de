"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/hooks/useAutoLogout.ts":
/*!************************************!*\
  !*** ./src/hooks/useAutoLogout.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\nconst useAutoLogout = function() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { timeoutMinutes = 5, warningMinutes = 0.5, enabled = true } = options;\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const warningTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const warningShownRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Convertir minutos a milisegundos\n    const timeoutMs = timeoutMinutes * 60 * 1000;\n    const warningMs = warningMinutes * 60 * 1000;\n    const clearTimeouts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[clearTimeouts]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            if (warningTimeoutRef.current) {\n                clearTimeout(warningTimeoutRef.current);\n                warningTimeoutRef.current = null;\n            }\n        }\n    }[\"useAutoLogout.useCallback[clearTimeouts]\"], []);\n    const handleLogout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleLogout]\": async ()=>{\n            try {\n                // Limpiar timeouts antes del logout\n                clearTimeouts();\n                await logout();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error('Sesión cerrada por inactividad', {\n                    duration: 4000,\n                    position: 'top-center',\n                    id: 'auto-logout' // Evitar duplicados\n                });\n            } catch (error) {\n                console.error('Error al cerrar sesión por inactividad:', error);\n                // Intentar logout forzado si hay error\n                try {\n                    window.location.href = '/login';\n                } catch (redirectError) {\n                    console.error('Error al redirigir:', redirectError);\n                }\n            }\n        }\n    }[\"useAutoLogout.useCallback[handleLogout]\"], [\n        logout,\n        clearTimeouts\n    ]);\n    const showWarning = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[showWarning]\": ()=>{\n            if (!warningShownRef.current) {\n                warningShownRef.current = true;\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.warning('Tu sesión se cerrará en 30 segundos por inactividad', {\n                    duration: 4000,\n                    position: 'top-center'\n                });\n            }\n        }\n    }[\"useAutoLogout.useCallback[showWarning]\"], []);\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n            if (!enabled || !user) return;\n            lastActivityRef.current = Date.now();\n            warningShownRef.current = false;\n            clearTimeouts();\n            // Configurar timeout para mostrar advertencia\n            if (warningMs > 0) {\n                warningTimeoutRef.current = setTimeout({\n                    \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                        showWarning();\n                    }\n                }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs - warningMs);\n            }\n            // Configurar timeout para logout\n            timeoutRef.current = setTimeout({\n                \"useAutoLogout.useCallback[resetTimer]\": ()=>{\n                    handleLogout();\n                }\n            }[\"useAutoLogout.useCallback[resetTimer]\"], timeoutMs);\n        }\n    }[\"useAutoLogout.useCallback[resetTimer]\"], [\n        enabled,\n        user,\n        timeoutMs,\n        warningMs,\n        clearTimeouts,\n        handleLogout,\n        showWarning\n    ]);\n    // Eventos que indican actividad del usuario\n    const activityEvents = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click'\n    ];\n    const handleActivity = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAutoLogout.useCallback[handleActivity]\": (event)=>{\n            // Evitar resetear el timer por movimientos mínimos del mouse\n            if (event.type === 'mousemove') {\n                const now = Date.now();\n                if (now - lastActivityRef.current < 1000) {\n                    return;\n                }\n            }\n            resetTimer();\n        }\n    }[\"useAutoLogout.useCallback[handleActivity]\"], [\n        resetTimer\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            if (!enabled || !user) {\n                clearTimeouts();\n                return;\n            }\n            // Inicializar timer\n            resetTimer();\n            // Agregar event listeners\n            activityEvents.forEach({\n                \"useAutoLogout.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useAutoLogout.useEffect\"]);\n            // Cleanup\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                    activityEvents.forEach({\n                        \"useAutoLogout.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useAutoLogout.useEffect\"]);\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        enabled,\n        user,\n        resetTimer,\n        handleActivity,\n        clearTimeouts\n    ]);\n    // Cleanup al desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAutoLogout.useEffect\": ()=>{\n            return ({\n                \"useAutoLogout.useEffect\": ()=>{\n                    clearTimeouts();\n                }\n            })[\"useAutoLogout.useEffect\"];\n        }\n    }[\"useAutoLogout.useEffect\"], [\n        clearTimeouts\n    ]);\n    return {\n        resetTimer,\n        clearTimeouts,\n        isEnabled: enabled && !!user\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAutoLogout);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAutoLogout.ts\n"));

/***/ })

});