import {
  supabase,
  ColeccionFlashcards,
  Flashcard,
  ProgresoFlashcard,
  FlashcardConProgreso,
  DificultadRespuesta,
  RevisionHistorial
} from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

/**
 * Crea una nueva colección de flashcards
 */
export async function crearColeccionFlashcards(titulo: string, descripcion?: string): Promise<string | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .insert([{
        titulo,
        descripcion,
        user_id: user.id
      }])
      .select();

    if (error) {
      console.error('Error al crear colección de flashcards:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al crear colección de flashcards:', error);
    return null;
  }
}

/**
 * Obtiene todas las colecciones de flashcards del usuario actual
 */
export async function obtenerColeccionesFlashcards(): Promise<ColeccionFlashcards[]> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*, flashcards(count)') // Fetch all columns from colecciones_flashcards and the count of related flashcards
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener colecciones de flashcards:', error);
      return [];
    }

    // Supabase returns the count in an array, e.g., flashcards: [{ count: 5 }]
    // We need to transform this to a direct property numero_flashcards
    return data?.map(coleccion => ({
      ...coleccion,
      // @ts-ignore Supabase types might not be perfect here for related counts
      numero_flashcards: coleccion.flashcards && Array.isArray(coleccion.flashcards) && coleccion.flashcards.length > 0
                         // @ts-ignore
                         ? coleccion.flashcards[0].count
                         : 0,
    })) || [];
  } catch (error) {
    console.error('Error al obtener colecciones de flashcards:', error);
    return [];
  }
}

/**
 * Obtiene una colección de flashcards por su ID (solo si pertenece al usuario actual)
 */
export async function obtenerColeccionFlashcardsPorId(id: string): Promise<ColeccionFlashcards | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener colección de flashcards:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener colección de flashcards:', error);
    return null;
  }
}

/**
 * Crea una nueva flashcard
 */
export async function crearFlashcard(coleccionId: string, pregunta: string, respuesta: string): Promise<string | null> {
  const { data, error } = await supabase
    .from('flashcards')
    .insert([{ coleccion_id: coleccionId, pregunta, respuesta }])
    .select();

  if (error) {
    console.error('Error al crear flashcard:', error);
    return null;
  }

  return data?.[0]?.id || null;
}

/**
 * Obtiene todas las flashcards de una colección
 */
export async function obtenerFlashcardsPorColeccionId(coleccionId: string): Promise<Flashcard[]> {
  const { data, error } = await supabase
    .from('flashcards')
    .select('*')
    .eq('coleccion_id', coleccionId)
    .order('creado_en', { ascending: true });

  if (error) {
    console.error('Error al obtener flashcards:', error);
    return [];
  }

  return data || [];
}

// Alias para mantener compatibilidad con el código existente
export const obtenerFlashcardsPorColeccion = obtenerFlashcardsPorColeccionId;

/**
 * Guarda múltiples flashcards en la base de datos
 */
export async function guardarFlashcards(flashcards: Omit<Flashcard, 'id' | 'creado_en' | 'actualizado_en'>[]): Promise<string[] | null> {
  const { data, error } = await supabase
    .from('flashcards')
    .insert(flashcards)
    .select();

  if (error) {
    console.error('Error al guardar flashcards:', error);
    return null;
  }

  return data?.map(card => card.id) || null;
}

/**
 * Obtiene una flashcard por su ID
 */
export async function obtenerFlashcardPorId(id: string): Promise<Flashcard | null> {
  const { data, error } = await supabase
    .from('flashcards')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error al obtener flashcard:', error);
    return null;
  }

  return data;
}

/**
 * Obtiene el progreso de una flashcard
 */
export async function obtenerProgresoFlashcard(flashcardId: string): Promise<ProgresoFlashcard | null> {
  const { data, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .eq('flashcard_id', flashcardId)
    .maybeSingle(); // Usar maybeSingle para evitar errores cuando no existe el registro

  if (error) {
    console.error('Error al obtener progreso de flashcard:', error);
    return null;
  }

  return data || null;
}

/**
 * Obtiene todas las flashcards con su progreso para una colección
 */
export async function obtenerFlashcardsParaEstudiar(coleccionId: string): Promise<FlashcardConProgreso[]> {
  // Obtener todas las flashcards de la colección
  const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);

  // Obtener el progreso de todas las flashcards en una sola consulta
  const { data: progresos, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .in('flashcard_id', flashcards.map(f => f.id));

  if (error) {
    console.error('Error al obtener progreso de flashcards:', error);
    return [];
  }

  // Fecha actual para comparar
  const ahora = new Date();
  const hoy = new Date(
    ahora.getFullYear(),
    ahora.getMonth(),
    ahora.getDate()
  );

  // Combinar flashcards con su progreso
  return flashcards.map(flashcard => {
    const progreso = progresos?.find(p => p.flashcard_id === flashcard.id);

    if (!progreso) {
      // Si no hay progreso, es una tarjeta nueva que debe estudiarse
      return {
        ...flashcard,
        debeEstudiar: true,
      };
    }

    // Determinar si la flashcard debe estudiarse hoy
    const proximaRevision = new Date(progreso.proxima_revision);
    const proximaRevisionSinHora = new Date(
      proximaRevision.getFullYear(),
      proximaRevision.getMonth(),
      proximaRevision.getDate()
    );
    const debeEstudiar = proximaRevisionSinHora <= hoy;

    return {
      ...flashcard,
      debeEstudiar,
      progreso: {
        factor_facilidad: progreso.factor_facilidad,
        intervalo: progreso.intervalo,
        repeticiones: progreso.repeticiones,
        estado: progreso.estado,
        proxima_revision: progreso.proxima_revision,
      },
    };
  });
}

/**
 * Obtiene flashcards más difíciles de una colección (basado en historial de revisiones)
 */
export async function obtenerFlashcardsMasDificiles(coleccionId: string, limite: number = 10): Promise<FlashcardConProgreso[]> {
  try {
    // Obtener todas las flashcards con progreso
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Obtener historial de revisiones para calcular dificultad
    const flashcardIds = flashcardsConProgreso.map(f => f.id);
    const { data: historial, error } = await supabase
      .from('historial_revisiones')
      .select('flashcard_id, dificultad')
      .in('flashcard_id', flashcardIds);

    if (error) {
      console.error('Error al obtener historial de revisiones:', error);
      return flashcardsConProgreso.slice(0, limite);
    }

    // Calcular estadísticas de dificultad por flashcard
    const estadisticasDificultad = new Map<string, { dificil: number; total: number }>();

    historial?.forEach(revision => {
      const stats = estadisticasDificultad.get(revision.flashcard_id) || { dificil: 0, total: 0 };
      stats.total++;
      if (revision.dificultad === 'dificil') {
        stats.dificil++;
      }
      estadisticasDificultad.set(revision.flashcard_id, stats);
    });

    // Ordenar por dificultad (ratio de respuestas difíciles)
    const flashcardsOrdenadas = flashcardsConProgreso
      .map(flashcard => {
        const stats = estadisticasDificultad.get(flashcard.id);
        const ratioDificultad = stats ? stats.dificil / stats.total : 0;
        return { ...flashcard, ratioDificultad };
      })
      .sort((a, b) => b.ratioDificultad - a.ratioDificultad)
      .slice(0, limite);

    return flashcardsOrdenadas;
  } catch (error) {
    console.error('Error al obtener flashcards más difíciles:', error);
    return [];
  }
}

/**
 * Obtiene flashcards aleatorias de una colección
 */
export async function obtenerFlashcardsAleatorias(coleccionId: string, limite: number = 10): Promise<FlashcardConProgreso[]> {
  try {
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Mezclar aleatoriamente y tomar el límite
    const flashcardsMezcladas = [...flashcardsConProgreso]
      .sort(() => Math.random() - 0.5)
      .slice(0, limite);

    return flashcardsMezcladas;
  } catch (error) {
    console.error('Error al obtener flashcards aleatorias:', error);
    return [];
  }
}

/**
 * Obtiene flashcards que no se han estudiado recientemente
 */
export async function obtenerFlashcardsNoRecientes(coleccionId: string, limite: number = 10): Promise<FlashcardConProgreso[]> {
  try {
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Obtener última revisión de cada flashcard
    const flashcardIds = flashcardsConProgreso.map(f => f.id);
    const { data: ultimasRevisiones, error } = await supabase
      .from('historial_revisiones')
      .select('flashcard_id, fecha')
      .in('flashcard_id', flashcardIds)
      .order('fecha', { ascending: false });

    if (error) {
      console.error('Error al obtener últimas revisiones:', error);
      return flashcardsConProgreso.slice(0, limite);
    }

    // Obtener la fecha más reciente por flashcard
    const ultimaRevisionPorFlashcard = new Map<string, string>();
    ultimasRevisiones?.forEach(revision => {
      if (!ultimaRevisionPorFlashcard.has(revision.flashcard_id)) {
        ultimaRevisionPorFlashcard.set(revision.flashcard_id, revision.fecha);
      }
    });

    // Ordenar por fecha de última revisión (más antiguas primero)
    const flashcardsOrdenadas = flashcardsConProgreso
      .map(flashcard => {
        const ultimaRevision = ultimaRevisionPorFlashcard.get(flashcard.id);
        return {
          ...flashcard,
          ultimaRevision: ultimaRevision ? new Date(ultimaRevision) : new Date(0)
        };
      })
      .sort((a, b) => a.ultimaRevision.getTime() - b.ultimaRevision.getTime())
      .slice(0, limite);

    return flashcardsOrdenadas;
  } catch (error) {
    console.error('Error al obtener flashcards no recientes:', error);
    return [];
  }
}

/**
 * Obtiene flashcards por estado específico
 */
export async function obtenerFlashcardsPorEstado(
  coleccionId: string,
  estado: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido',
  limite: number = 10
): Promise<FlashcardConProgreso[]> {
  try {
    const flashcardsConProgreso = await obtenerFlashcardsParaEstudiar(coleccionId);

    // Filtrar por estado y limitar
    const flashcardsFiltradas = flashcardsConProgreso
      .filter(flashcard => {
        if (!flashcard.progreso) {
          return estado === 'nuevo';
        }
        return flashcard.progreso.estado === estado;
      })
      .slice(0, limite);

    return flashcardsFiltradas;
  } catch (error) {
    console.error('Error al obtener flashcards por estado:', error);
    return [];
  }
}

/**
 * Registra una respuesta a una flashcard y actualiza su progreso
 * Versión mejorada para evitar errores 409
 */
export async function registrarRespuestaFlashcard(
  flashcardId: string,
  dificultad: DificultadRespuesta
): Promise<boolean> {
  try {
    // Primero intentamos obtener el progreso existente
    let factorFacilidad = 2.5;
    let intervalo = 1;
    let repeticiones = 0;
    let estado: 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido' = 'nuevo';
    let progresoExiste = false;

    // Intentar obtener progreso existente
    const { data: progresoExistente, error: errorConsulta } = await supabase
      .from('progreso_flashcards')
      .select('factor_facilidad, intervalo, repeticiones, estado')
      .eq('flashcard_id', flashcardId)
      .single();

    if (!errorConsulta && progresoExistente) {
      factorFacilidad = progresoExistente.factor_facilidad || 2.5;
      intervalo = progresoExistente.intervalo || 1;
      repeticiones = progresoExistente.repeticiones || 0;
      estado = progresoExistente.estado || 'nuevo';
      progresoExiste = true;
    }

  // Aplicar el algoritmo SM-2 para calcular el nuevo progreso
  let nuevoFactorFacilidad = factorFacilidad;
  let nuevoIntervalo = intervalo;
  let nuevasRepeticiones = repeticiones;
  let nuevoEstado = estado;

  // Ajustar el factor de facilidad según la dificultad reportada
  if (dificultad === 'dificil') {
    nuevoFactorFacilidad = Math.max(1.3, factorFacilidad - 0.3);
    nuevasRepeticiones = 0;
    nuevoIntervalo = 1;
    nuevoEstado = 'aprendiendo';
  } else {
    nuevasRepeticiones++;

    if (dificultad === 'normal') {
      nuevoFactorFacilidad = factorFacilidad - 0.15;
    } else if (dificultad === 'facil') {
      nuevoFactorFacilidad = factorFacilidad + 0.1;
    }

    nuevoFactorFacilidad = Math.max(1.3, Math.min(2.5, nuevoFactorFacilidad));

    // Calcular el nuevo intervalo
    if (nuevasRepeticiones === 1) {
      nuevoIntervalo = 1;
      nuevoEstado = 'aprendiendo';
    } else if (nuevasRepeticiones === 2) {
      nuevoIntervalo = 6;
      nuevoEstado = 'repasando';
    } else {
      nuevoIntervalo = Math.round(intervalo * nuevoFactorFacilidad);
      nuevoEstado = nuevoIntervalo > 30 ? 'aprendido' : 'repasando';
    }
  }

  // Calcular la próxima fecha de revisión
  const ahora = new Date();
  const proximaRevision = new Date(ahora);
  proximaRevision.setDate(proximaRevision.getDate() + nuevoIntervalo);

    // Guardar el nuevo progreso usando insert o update según corresponda
    let errorProgreso = null;

    if (progresoExiste) {
      // Actualizar progreso existente
      const { error } = await supabase
        .from('progreso_flashcards')
        .update({
          factor_facilidad: nuevoFactorFacilidad,
          intervalo: nuevoIntervalo,
          repeticiones: nuevasRepeticiones,
          estado: nuevoEstado,
          ultima_revision: ahora.toISOString(),
          proxima_revision: proximaRevision.toISOString(),
        })
        .eq('flashcard_id', flashcardId);

      errorProgreso = error;
    } else {
      // Crear nuevo progreso
      const { error } = await supabase
        .from('progreso_flashcards')
        .insert({
          flashcard_id: flashcardId,
          factor_facilidad: nuevoFactorFacilidad,
          intervalo: nuevoIntervalo,
          repeticiones: nuevasRepeticiones,
          estado: nuevoEstado,
          ultima_revision: ahora.toISOString(),
          proxima_revision: proximaRevision.toISOString(),
        });

      errorProgreso = error;
    }

    if (errorProgreso) {
      console.error('Error al guardar progreso:', errorProgreso);
      return false;
    }

    // Guardar en el historial de revisiones
    const { error: errorHistorial } = await supabase
      .from('historial_revisiones')
      .insert({
        flashcard_id: flashcardId,
        dificultad,
        factor_facilidad: nuevoFactorFacilidad,
        intervalo: nuevoIntervalo,
        repeticiones: nuevasRepeticiones,
        fecha: ahora.toISOString(),
      });

    if (errorHistorial) {
      // No retornamos false aquí porque el progreso ya se guardó correctamente
    }

    return true;
  } catch (error) {
    return false;
  }
}

// Alias para mantener compatibilidad con el código existente
export const actualizarProgresoFlashcard = registrarRespuestaFlashcard;

/**
 * Guarda una revisión en el historial
 */
export async function guardarRevisionHistorial(
  flashcardId: string,
  dificultad: DificultadRespuesta,
  factorFacilidad: number,
  intervalo: number,
  repeticiones: number
): Promise<string | null> {
  const { data, error } = await supabase
    .from('historial_revisiones')
    .insert([{
      flashcard_id: flashcardId,
      dificultad,
      factor_facilidad: factorFacilidad,
      intervalo,
      repeticiones
    }])
    .select();

  if (error) {
    console.error('Error al guardar revisión en historial:', error);
    return null;
  }

  return data?.[0]?.id || null;
}

/**
 * Reinicia el progreso de una flashcard
 */
export async function reiniciarProgresoFlashcard(flashcardId: string): Promise<boolean> {
  const { error } = await supabase
    .from('progreso_flashcards')
    .update({
      factor_facilidad: 2.5,
      intervalo: 0,
      repeticiones: 0,
      estado: 'nuevo',
      ultima_revision: new Date().toISOString(),
      proxima_revision: new Date().toISOString()
    })
    .eq('flashcard_id', flashcardId);

  if (error) {
    console.error('Error al reiniciar progreso de flashcard:', error);
    return false;
  }

  return true;
}

/**
 * Actualiza una flashcard existente
 */
export async function actualizarFlashcard(
  flashcardId: string,
  pregunta: string,
  respuesta: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('flashcards')
      .update({
        pregunta,
        respuesta,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', flashcardId);

    if (error) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Elimina una flashcard y todo su progreso asociado
 */
export async function eliminarFlashcard(flashcardId: string): Promise<boolean> {
  try {
    // Primero eliminar el progreso asociado
    const { error: errorProgreso } = await supabase
      .from('progreso_flashcards')
      .delete()
      .eq('flashcard_id', flashcardId);

    if (errorProgreso) {
      return false;
    }

    // Eliminar el historial de revisiones
    const { error: errorHistorial } = await supabase
      .from('historial_revisiones')
      .delete()
      .eq('flashcard_id', flashcardId);

    if (errorHistorial) {
      return false;
    }

    // Finalmente eliminar la flashcard
    const { error: errorFlashcard, count } = await supabase
      .from('flashcards')
      .delete({ count: 'exact' })
      .eq('id', flashcardId);

    if (errorFlashcard) {
      return false;
    }

    if (count === 0) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Elimina una colección completa de flashcards y todo su contenido asociado
 */
export async function eliminarColeccionFlashcards(coleccionId: string): Promise<boolean> {
  try {
    // Obtener el usuario actual para verificar permisos
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      return false;
    }

    // Primero obtener todas las flashcards de la colección
    const { data: flashcards, error: errorFlashcards } = await supabase
      .from('flashcards')
      .select('id')
      .eq('coleccion_id', coleccionId);

    if (errorFlashcards) {
      return false;
    }

    const flashcardIds = flashcards?.map(fc => fc.id) || [];

    // Eliminar progreso de todas las flashcards
    if (flashcardIds.length > 0) {
      const { error: errorProgreso } = await supabase
        .from('progreso_flashcards')
        .delete()
        .in('flashcard_id', flashcardIds);

      if (errorProgreso) {
        return false;
      }

      // Eliminar historial de todas las flashcards
      const { error: errorHistorial } = await supabase
        .from('historial_revisiones')
        .delete()
        .in('flashcard_id', flashcardIds);

      if (errorHistorial) {
        return false;
      }

      // Eliminar todas las flashcards de la colección
      const { error: errorFlashcardsDelete } = await supabase
        .from('flashcards')
        .delete()
        .eq('coleccion_id', coleccionId);

      if (errorFlashcardsDelete) {
        return false;
      }
    }

    // Finalmente eliminar la colección
    const { error: errorColeccion, count } = await supabase
      .from('colecciones_flashcards')
      .delete({ count: 'exact' })
      .eq('id', coleccionId)
      .eq('user_id', user.id); // Asegurar que solo se eliminen colecciones del usuario actual

    if (errorColeccion) {
      return false;
    }

    if (count === 0) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}
